<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Models\ClientDeal;
use App\Models\Deal;
use App\Models\DealDiscussion;
use App\Models\DealFile;
use App\Models\DealTask;
use App\Models\Pipeline;
use App\Models\UserDeal;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;

class PipelineController extends Controller
{
    use ApiResponser;

    public function __construct()
    {
        $this->middleware(
            [
                'auth',
                'XSS',
            ]
        );
    }

    /**
     * Display pipelines in the pipeline view (singular route)
     * This method serves the pipeline.index route
     *
     * @return \Illuminate\Http\Response
     */
    public function pipelineIndex()
    {
        if(\Auth::user()->can('manage pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            // Filter pipelines by current user's creator (company) and exclude soft-deleted
            $pipelines = Pipeline::where('created_by', '=', $currentUser->creatorId())
                ->where('is_deleted', 0)
                ->with(['stages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->with(['leadStages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->get();

            return view('pipeline.index')->with('pipelines', $pipelines);
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display a listing of the resource.
     * Automatically filters pipelines based on current authenticated user
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if(\Auth::user()->can('manage pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            // Filter pipelines by current user's creator (company) and exclude soft-deleted
            $pipelines = Pipeline::where('created_by', '=', $currentUser->creatorId())
                ->where('is_deleted', 0)
                ->with(['stages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->with(['leadStages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->get();

            // Check if this is an API request
            if ($request->expectsJson() || $request->is('api/*')) {
                $pipelinesData = $pipelines->map(function ($pipeline) {
                    return [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'pipeline_id' => $stage->pipeline_id,
                                'order' => $stage->order,
                                'created_by' => $stage->created_by,
                                'created_at' => $stage->created_at,
                                'updated_at' => $stage->updated_at,
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'pipeline_id' => $leadStage->pipeline_id,
                                'order' => $leadStage->order,
                                'created_by' => $leadStage->created_by,
                                'created_at' => $leadStage->created_at,
                                'updated_at' => $leadStage->updated_at,
                            ];
                        }),
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                    ];
                });

                return $this->success([
                    'pipelines' => $pipelinesData,
                    'total' => $pipelinesData->count(),
                    'user_info' => [
                        'id' => $currentUser->id,
                        'name' => $currentUser->name,
                        'email' => $currentUser->email,
                        'type' => $currentUser->type,
                        'creator_id' => $currentUser->creatorId(),
                        'owner_id' => $currentUser->ownerId()
                    ],
                    'timestamp' => now()->toDateTimeString(),
                ], 'Pipelines retrieved successfully for user: ' . $currentUser->name);
            }

            return view('pipeline.index')->with('pipelines', $pipelines);
        }
        else
        {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error('Permission Denied.', 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if(\Auth::user()->can('create pipeline'))
        {
            return view('pipeline.create');
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Store a newly created resource in storage.
     * Automatically assigns pipeline to current user's company
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        if(\Auth::user()->can('create pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            $validator = \Validator::make(
                $request->all(), [
                    'name' => 'required|max:20',
                    'stages.*.name' => 'required|max:20',
                    'stages.*.order' => 'required|integer|min:1',
                ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json(['error' => $messages->first()], 422);
                }
                return redirect()->route('pipeline.index')->with('error', $messages->first());
            }

            try {
                \DB::beginTransaction();

                // Create pipeline for current user's company
                $pipeline             = new Pipeline();
                $pipeline->name       = $request->name;
                $pipeline->created_by = $currentUser->creatorId(); // Assign to current user's company
                $pipeline->save();

                // Create stages if provided
                if ($request->has('stages') && is_array($request->stages)) {
                    $this->createPipelineStages($pipeline, $request->stages);
                }

                \DB::commit();

                $message = __('Pipeline and stages created successfully!');

                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => $message,
                        'data' => [
                            'pipeline' => [
                                'id' => $pipeline->id,
                                'name' => $pipeline->name,
                                'created_by' => $pipeline->created_by,
                                'created_at' => $pipeline->created_at,
                                'updated_at' => $pipeline->updated_at,
                            ],
                            'user_info' => [
                                'id' => $currentUser->id,
                                'name' => $currentUser->name,
                                'creator_id' => $currentUser->creatorId(),
                            ],
                            'timestamp' => now()->toDateTimeString(),
                        ]
                    ]);
                }

                return redirect()->route('pipeline.index')->with('success', $message);

            } catch (\Exception $e) {
                \DB::rollback();

                $errorMessage = __('An error occurred while creating the pipeline and stages.');

                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json(['error' => $errorMessage], 500);
                }

                return redirect()->route('pipeline.index')->with('error', $errorMessage);
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified resource.
     * Automatically checks if pipeline belongs to current user's company
     *
     * @param \App\Pipeline $pipeline
     * @param Request $request
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function show(Pipeline $pipeline, Request $request)
    {
        if(\Auth::user()->can('manage pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            // Check if pipeline belongs to current user's company
            if($pipeline->created_by == $currentUser->creatorId())
            {
                // Load relationships filtered by current user
                $pipeline->load(['stages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }]);

                $pipeline->load(['leadStages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }]);

                // Check if this is an API request
                if ($request->expectsJson() || $request->is('api/*')) {
                    $pipelineData = [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'pipeline_id' => $stage->pipeline_id,
                                'order' => $stage->order,
                                'created_by' => $stage->created_by,
                                'created_at' => $stage->created_at,
                                'updated_at' => $stage->updated_at,
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'pipeline_id' => $leadStage->pipeline_id,
                                'order' => $leadStage->order,
                                'created_by' => $leadStage->created_by,
                                'created_at' => $leadStage->created_at,
                                'updated_at' => $leadStage->updated_at,
                            ];
                        }),
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                    ];

                    return $this->success([
                        'pipeline' => $pipelineData,
                        'user_info' => [
                            'id' => $currentUser->id,
                            'name' => $currentUser->name,
                            'email' => $currentUser->email,
                            'creator_id' => $currentUser->creatorId(),
                        ],
                        'timestamp' => now()->toDateTimeString(),
                    ], 'Pipeline retrieved successfully for user: ' . $currentUser->name);
                }
            }
            else
            {
                if ($request->expectsJson() || $request->is('api/*')) {
                    return $this->error('Pipeline not found or access denied for this user.', 403);
                }
            }
        }
        else
        {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error('Permission Denied.', 403);
            }
        }

        return redirect()->route('pipeline.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Pipeline $pipeline)
    {
        if(\Auth::user()->can('edit pipeline'))
        {
            if($pipeline->created_by == \Auth::user()->creatorId())
            {
                return view('pipelines.edit', compact('pipeline'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Pipeline $pipeline)
    {
        if(\Auth::user()->can('edit pipeline'))
        {

            if($pipeline->created_by == \Auth::user()->creatorId())
            {

                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:20',
                        'stages.*.name' => 'required|max:20',
                        'stages.*.order' => 'required|integer|min:1',
                    ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                        return response()->json(['error' => $messages->first()], 422);
                    }
                    return redirect()->route('pipeline.index')->with('error', $messages->first());
                }

                try {
                    \DB::beginTransaction();

                    // Update pipeline name
                    $pipeline->name = $request->name;
                    $pipeline->save();

                    // Handle stages if provided
                    if ($request->has('stages') && is_array($request->stages)) {
                        $this->updatePipelineStages($pipeline, $request->stages);
                    }

                    \DB::commit();

                    $message = __('Pipeline and stages updated successfully!');

                    if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                        return response()->json([
                            'success' => true,
                            'message' => $message,
                            'data' => ['pipeline' => $pipeline]
                        ]);
                    }

                    return redirect()->route('pipeline.index')->with('success', $message);

                } catch (\Exception $e) {
                    \DB::rollback();

                    $errorMessage = __('An error occurred while updating the pipeline and stages.');

                    if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                        return response()->json(['error' => $errorMessage], 500);
                    }

                    return redirect()->route('pipeline.index')->with('error', $errorMessage);
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Create pipeline stages
     *
     * @param \App\Pipeline $pipeline
     * @param array $stages
     *
     * @return void
     */
    private function createPipelineStages(Pipeline $pipeline, array $stages)
    {
        foreach ($stages as $stageData) {
            $stage = new \App\Models\LeadStage();
            $stage->name = $stageData['name'];
            $stage->pipeline_id = $pipeline->id;
            $stage->created_by = \Auth::user()->creatorId();
            $stage->order = $stageData['order'];
            $stage->save();
        }
    }

    /**
     * Update pipeline stages
     *
     * @param \App\Pipeline $pipeline
     * @param array $stages
     *
     * @return void
     */
    private function updatePipelineStages(Pipeline $pipeline, array $stages)
    {
        $existingStageIds = [];

        foreach ($stages as $stageData) {
            if (!empty($stageData['id'])) {
                // Update existing stage
                $stage = \App\Models\LeadStage::where('id', $stageData['id'])
                    ->where('pipeline_id', $pipeline->id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->first();

                if ($stage) {
                    $stage->name = $stageData['name'];
                    $stage->order = $stageData['order'];
                    $stage->save();
                    $existingStageIds[] = $stage->id;
                }
            } else {
                // Create new stage
                $stage = new \App\Models\LeadStage();
                $stage->name = $stageData['name'];
                $stage->pipeline_id = $pipeline->id;
                $stage->created_by = \Auth::user()->creatorId();
                $stage->order = $stageData['order'];
                $stage->save();
                $existingStageIds[] = $stage->id;
            }
        }

        // Delete stages that are no longer in the list
        \App\Models\LeadStage::where('pipeline_id', $pipeline->id)
            ->where('created_by', \Auth::user()->creatorId())
            ->whereNotIn('id', $existingStageIds)
            ->delete();
    }

    /**
     * Get stages for a specific pipeline
     *
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function getStages(Pipeline $pipeline)
    {
        // Add debugging
        \Log::info('getStages called for pipeline: ' . $pipeline->id);
        \Log::info('User permissions - manage pipeline: ' . (\Auth::user()->can('manage pipeline') ? 'true' : 'false'));
        \Log::info('User permissions - create lead: ' . (\Auth::user()->can('create lead') ? 'true' : 'false'));
        \Log::info('User permissions - edit lead: ' . (\Auth::user()->can('edit lead') ? 'true' : 'false'));
        \Log::info('Pipeline created_by: ' . $pipeline->created_by . ', User creatorId: ' . \Auth::user()->creatorId());
        
        // Allow access if user can manage pipeline OR can create/edit leads
        if(\Auth::user()->can('manage pipeline') || \Auth::user()->can('create lead') || \Auth::user()->can('edit lead'))
        {
            if($pipeline->created_by == \Auth::user()->creatorId())
            {
                // Check if pipeline exists and has any stages
                $allStagesForPipeline = \App\Models\LeadStage::where('pipeline_id', $pipeline->id)->get();
                \Log::info('Total stages for pipeline ' . $pipeline->id . ': ' . $allStagesForPipeline->count());
                
                $leadStages = \App\Models\LeadStage::where('pipeline_id', $pipeline->id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->orderBy('order')
                    ->get();

                \Log::info('Found ' . $leadStages->count() . ' stages for pipeline ' . $pipeline->id . ' with created_by ' . \Auth::user()->creatorId());

                return response()->json([
                    'success' => true,
                    'stages' => $leadStages
                ]);
            }
            else
            {
                \Log::warning('Permission denied - pipeline created_by mismatch');
                return response()->json(['error' => __('Permission Denied.')], 403);
            }
        }
        else
        {
            \Log::warning('Permission denied - user lacks required permissions');
            return response()->json(['error' => __('Permission Denied.')], 403);
        }
    }

    /**
     * Remove the specified resource from storage.
     * Implements DSA logic for cascading deletion
     *
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Pipeline $pipeline, Request $request)
    {
        if(\Auth::user()->can('delete pipeline'))
        {
            if($pipeline->created_by == \Auth::user()->creatorId())
            {
                $permanentDelete = $request->has('permanent_delete') && $request->permanent_delete == '1';

                if ($permanentDelete) {
                    // Permanent deletion with DSA cascading logic
                    $this->permanentDeletePipeline($pipeline);
                    $message = __('Pipeline and all associated data permanently deleted!');
                } else {
                    // Soft delete - just mark as deleted
                    $pipeline->is_deleted = 1;
                    $pipeline->save();
                    $message = __('Pipeline soft deleted successfully!');
                }

                if ($request->expectsJson() || $request->is('api/*')) {
                    return response()->json(['success' => true, 'message' => $message]);
                }

                return redirect()->route('pipeline.index')->with('success', $message);
            }
            else
            {
                if ($request->expectsJson() || $request->is('api/*')) {
                    return response()->json(['error' => __('Permission Denied.')], 403);
                }
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json(['error' => __('Permission Denied.')], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Permanently delete pipeline and all associated data using DSA logic
     * DSA (Data Structure Algorithm) approach for cascading deletion
     *
     * @param Pipeline $pipeline
     * @return void
     */
    private function permanentDeletePipeline(Pipeline $pipeline)
    {
        try {
            \DB::beginTransaction();

            // Step 1: Get all lead stages for this pipeline
            $leadStages = \App\Models\LeadStage::where('pipeline_id', $pipeline->id)
                ->where('created_by', \Auth::user()->ownerId())
                ->get();

            // Step 2: For each lead stage, delete all associated leads
            foreach ($leadStages as $leadStage) {
                // Get all leads in this stage
                $leads = \App\Models\Lead::where('stage_id', $leadStage->id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->get();

                // Delete each lead and its associated data
                foreach ($leads as $lead) {
                    // Delete lead files
                    \App\Models\LeadFile::where('lead_id', $lead->id)->delete();

                    // Delete lead activity logs
                    \App\Models\LeadActivityLog::where('lead_id', $lead->id)->delete();

                    // Delete lead discussions
                    \App\Models\LeadDiscussion::where('lead_id', $lead->id)->delete();

                    // Delete lead emails
                    \App\Models\LeadEmail::where('lead_id', $lead->id)->delete();

                    // Delete lead calls
                    \App\Models\LeadCall::where('lead_id', $lead->id)->delete();

                    // Delete user-lead relationships
                    \DB::table('user_leads')->where('lead_id', $lead->id)->delete();

                    // Delete lead tasks if they exist
                    if (\Schema::hasTable('lead_tasks')) {
                        \DB::table('lead_tasks')->where('lead_id', $lead->id)->delete();
                    }

                    // Delete lead comments if they exist
                    if (\Schema::hasTable('lead_comments')) {
                        \DB::table('lead_comments')->where('lead_id', $lead->id)->delete();
                    }

                    // Finally delete the lead itself
                    $lead->delete();
                }
            }

            // Step 3: Delete all lead stages for this pipeline
            \App\Models\LeadStage::where('pipeline_id', $pipeline->id)
                ->where('created_by', \Auth::user()->ownerId())
                ->delete();

            // Step 4: Get all deal stages for this pipeline
            $dealStages = \App\Models\Stage::where('pipeline_id', $pipeline->id)
                ->where('created_by', \Auth::user()->ownerId())
                ->get();

            // Step 5: For each deal stage, delete all associated deals
            foreach ($dealStages as $dealStage) {
                // Get all deals in this stage
                $deals = \App\Models\Deal::where('stage_id', $dealStage->id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->get();

                // Delete each deal and its associated data
                foreach ($deals as $deal) {
                    // Delete deal files
                    \App\Models\DealFile::where('deal_id', $deal->id)->delete();

                    // Delete deal tasks
                    \App\Models\DealTask::where('deal_id', $deal->id)->delete();

                    // Delete deal discussions
                    \App\Models\DealDiscussion::where('deal_id', $deal->id)->delete();

                    // Delete deal emails
                    \App\Models\DealEmail::where('deal_id', $deal->id)->delete();

                    // Delete deal calls
                    \App\Models\DealCall::where('deal_id', $deal->id)->delete();

                    // Delete user-deal relationships
                    \DB::table('user_deals')->where('deal_id', $deal->id)->delete();

                    // Delete client-deal relationships
                    \DB::table('client_deals')->where('deal_id', $deal->id)->delete();

                    // Delete deal comments if they exist
                    if (\Schema::hasTable('deal_comments')) {
                        \DB::table('deal_comments')->where('deal_id', $deal->id)->delete();
                    }

                    // Finally delete the deal itself
                    $deal->delete();
                }
            }

            // Step 6: Delete all deal stages for this pipeline
            \App\Models\Stage::where('pipeline_id', $pipeline->id)
                ->where('created_by', \Auth::user()->ownerId())
                ->delete();

            // Step 7: Finally delete the pipeline itself
            $pipeline->delete();

            \DB::commit();

        } catch (\Exception $e) {
            \DB::rollback();
            throw $e;
        }
    }
}
