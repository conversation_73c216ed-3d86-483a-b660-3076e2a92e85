<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'created_by'
    ];

    /**
     * Get the leads in this contact group
     */
    public function leads()
    {
        return $this->hasMany(Lead::class, 'contact_group_id');
    }

    /**
     * Get the total number of contacts in this group
     */
    public function getTotalContactsAttribute()
    {
        return $this->leads()->count();
    }

    /**
     * Get the user who created this group
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
