
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Leads')); ?> <?php if($pipeline): ?>
        - <?php echo e($pipeline->name); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/plugins/dragula.min.css')); ?>" id="main-style-link">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <style>
        :root {
            --kanban-bg: rgba(255,255,255,0.7);
            --kanban-blur: 16px;
            --kanban-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
            --kanban-border: 1px solid rgba(255,255,255,0.18);
            --kanban-radius: 18px;
            --kanban-gradient: linear-gradient(135deg, rgba(245,247,250,0.8) 0%, rgba(230,240,255,0.7) 100%);
            --kanban-label-radius: 12px;
            --kanban-label-font: 13px;
            --kanban-label-padding: 3px 12px;
        }
        .kanban-wrapper {
            display: flex;
            gap: 24px;
            overflow-x: auto;
            padding-bottom: 16px;
            scrollbar-width: thin;
        }
        .kanban-wrapper::-webkit-scrollbar {
            height: 8px;
        }
        .kanban-wrapper::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .kanban-col {
            min-width: 320px;
            max-width: 350px;
            flex: 1 0 320px;
            background: var(--kanban-gradient);
            border-radius: var(--kanban-radius);
            box-shadow: var(--kanban-shadow);
            border: var(--kanban-border);
            backdrop-filter: blur(var(--kanban-blur));
            padding: 0 0 12px 0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .kanban-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: transparent;
            border-radius: var(--kanban-radius) var(--kanban-radius) 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-header h4 {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
        }
        .kanban-header .count {
            background: #e3e9f7;
            color: #3a3a3a;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        .sales-item-wrp {
            padding: 16px 12px 0 12px;
            min-height: 80px;
            max-height: 480px; /* Show ~3 cards, then scroll */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #e0e7ef #f8fafc;
            transition: max-height 0.2s;
        }
        .sales-item-wrp::-webkit-scrollbar {
            width: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 8px;
        }
        .kanban-card {
            background: var(--kanban-bg);
            border-radius: var(--kanban-radius);
            box-shadow: 0 2px 12px 0 rgba(31, 38, 135, 0.08);
            border: var(--kanban-border);
            margin-bottom: 18px;
            padding: 18px 16px 12px 16px;
            position: relative;
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: grab;
            backdrop-filter: blur(var(--kanban-blur));
        }
        .kanban-card.dragging {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            transform: scale(1.03);
            z-index: 10;
        }
        .kanban-card .card-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }
        .kanban-card .lead-title {
            font-size: 1.05rem;
            font-weight: 600;
            color: #222;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .kanban-card .lead-title i {
            color: #6c63ff;
            font-size: 1rem;
        }
        .kanban-card .badge-wrp {
            margin-top: 6px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .kanban-label {
            border-radius: var(--kanban-label-radius);
            font-size: var(--kanban-label-font);
            padding: var(--kanban-label-padding);
            font-weight: 500;
            color: #fff;
            background: #6c63ff;
            opacity: 0.95;
        }
        .kanban-label.bg-light-success { background: #28a745; }
        .kanban-label.bg-light-danger { background: #dc3545; }
        .kanban-label.bg-light-warning { background: #ffc107; color: #222; }
        .kanban-label.bg-light-info { background: #17a2b8; }
        .kanban-label.bg-light-primary { background: #6c63ff; }
        .kanban-label.bg-light-secondary { background: #6c757d; }
        .kanban-label.bg-light-brown { background: #a17a69; }
        .kanban-label.bg-light-blue { background: #007bff; }
        .kanban-label.bg-light-purple { background: #6f42c1; }
        .kanban-card .contact-info {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        .kanban-card .contact-item {
            display: flex;
            align-items: center;
            gap: 7px;
            font-size: 14px;
            color: #555;
        }
        .kanban-card .contact-item i {
            color: #888;
        }
        .kanban-card .card-bottom {
            margin-top: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-card .communication-buttons {
            display: flex;
            gap: 8px;
        }
        .kanban-card .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.12);
            background: #6c63ff;
        }
        .kanban-card .communication-btn.call { background: #28a745; }
        .kanban-card .communication-btn.whatsapp { background: #25D366; }
        .kanban-card .communication-btn.email { background: #6f42c1; }
        .kanban-card .communication-btn.activity { background: #fd7e14; }
        .kanban-card .communication-btn i {
            font-size: 14px;
        }
        .kanban-card .user-group {
            display: flex;
            gap: 4px;
        }
        .kanban-card .user-group i {
            font-size: 22px;
            color: #6c63ff;
        }
        .kanban-card .drag-handle {
            cursor: grab;
            color: #bdbdbd;
            font-size: 18px;
            margin-right: 8px;
            transition: color 0.2s;
        }
        .kanban-card .drag-handle:hover {
            color: #6c63ff;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            margin: 3px !important;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: black !important;
        }
        /* Responsive */
        @media (max-width: 900px) {
            .kanban-wrapper {
                gap: 12px;
            }
            .kanban-col {
                min-width: 260px;
                max-width: 100vw;
            }
            .sales-item-wrp {
                max-height: 340px;
            }
        }
        @media (max-width: 600px) {
            .kanban-wrapper {
                gap: 8px;
            }
            .kanban-col {
                min-width: 90vw;
                max-width: 98vw;
                padding: 0;
            }
            .kanban-header {
                padding: 14px 10px 8px 10px;
            }
            .sales-item-wrp {
                max-height: 220px;
                padding: 8px 4px 0 4px;
            }
            .kanban-card {
                padding: 12px 8px 8px 8px;
            }
        }
    </style>
    <style>
        .modern-comm-modal {
            background: rgba(255,255,255,0.85);
            border-radius: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            border: 1px solid rgba(255,255,255,0.18);
            backdrop-filter: blur(12px);
        }
        .comm-lead-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6c63ff 0%, #17a2b8 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(31,38,135,0.10);
        }
        .comm-lead-name {
            font-weight: 600;
            font-size: 1.1rem;
        }
        .comm-lead-contact {
            font-size: 0.95rem;
        }
        .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 14px;
            margin-top: 10px;
        }
        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            border-radius: 12px;
            background: rgba(245,247,250,0.95);
            color: #333;
            text-decoration: none;
            font-size: 1.08rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(31,38,135,0.06);
            transition: all 0.2s;
            border: 1px solid #f0f0f0;
        }
        .communication-option-item:hover {
            background: linear-gradient(135deg, #e3e9f7 0%, #f8fafc 100%);
            transform: translateX(5px) scale(1.03);
            box-shadow: 0 4px 16px rgba(31,38,135,0.10);
        }
        .communication-option-item i {
            font-size: 1.5rem;
            margin-right: 18px;
            width: 32px;
            text-align: center;
        }
        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        #sms-option i { color: #17a2b8; }
        @media (max-width: 600px) {
            .modern-comm-modal {
                border-radius: 10px;
            }
            .communication-option-item {
                padding: 12px 10px;
                font-size: 1rem;
            }
            .comm-lead-avatar {
                width: 38px;
                height: 38px;
                font-size: 1.1rem;
            }
        }
        
        /* Oval Pipeline Select Styling */
        #default_pipeline_id {
            border-radius: 25px !important;
            padding: 8px 20px 8px 20px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%236c63ff'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e") !important;
            background-position: right 12px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 12px !important;
            padding-right: 35px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }
        
        #default_pipeline_id:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        #default_pipeline_id:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: #ffffff !important;
        }
        
        #default_pipeline_id option {
            background: white !important;
            color: #374151 !important;
            padding: 8px 12px !important;
            border-radius: 8px !important;
        }
        
        /* Custom form wrapper styling for oval design */
        #change-pipeline {
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
            margin-bottom: 0px;
        }
        
        /* Ensure proper alignment with other action buttons */
        .float-end {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }
        
        /* Search Bar Styling - Same oval shape as pipeline select */
        .search-container {
            position: relative !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
        }
        
        .search-input {
            border-radius: 25px !important;
            padding: 6px 14px 6px 14px !important;
            padding-right: 45px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 200px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            transition: all 0.3s ease !important;
        }
        
        .search-input:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
        }
        
        .search-input:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        .search-input::placeholder {
            color: #9ca3af !important;
            font-weight: 400 !important;
        }
        
        .search-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
        }
        
        .clear-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
            transition: color 0.3s ease !important;
        }
        
        .clear-icon:hover {
            color: #dc3545 !important;
        }

        /* Enhanced Filter Styles */
        #filterOffcanvasKanban .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        #filterOffcanvasKanban .form-control {
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 0.5rem 0.75rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        #filterOffcanvasKanban .form-control:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }

        #filterOffcanvasKanban .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        #filterOffcanvasKanban .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        #filterOffcanvasKanban .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        #filterOffcanvasKanban .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        #custom-fields-container .mb-2 {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 0.75rem !important;
        }

        #custom-fields-container .form-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        /* Flatpickr Custom Styles */
        .flatpickr-calendar {
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
        }

        .flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange {
            background: #10b981;
            border-color: #10b981;
        }

        .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover {
            background: #059669;
            border-color: #059669;
        }

        .flatpickr-day.inRange {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.1);
        }

        .flatpickr-months .flatpickr-month {
            background: #10b981;
            color: white;
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months {
            background: #10b981;
        }

        .flatpickr-current-month .numInputWrapper span.arrowUp:after {
            border-bottom-color: white;
        }

        .flatpickr-current-month .numInputWrapper span.arrowDown:after {
            border-top-color: white;
        }

        /* Select2 Custom Styles */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--multiple {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            min-height: 38px;
            padding: 2px 8px;
        }

        .select2-container--default .select2-selection--multiple:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #10b981;
            border: 1px solid #10b981;
            border-radius: 6px;
            color: white;
            font-size: 0.875rem;
            padding: 4px 20px;
            margin: 2px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #f3f4f6;
        }



        /* Placeholder styling */
        .select2-container--default .select2-selection--multiple .select2-selection__placeholder {
            color: #6c757d;
            font-style: italic;
        }

        .select2-dropdown {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #10b981;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 6px 12px;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field:focus {
            border-color: #10b981;
            outline: none;
        }

        /* Offcanvas specific Select2 adjustments */
        #filterOffcanvasKanban .select2-container {
            z-index: 1056 !important;
        }

        #filterOffcanvasKanban .select2-dropdown {
            z-index: 1057 !important;
        }

        /* Loading state for kanban */
        .kanban-wrapper.loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .kanban-wrapper.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/dragula.min.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        ! function(a) {
            "use strict";
            var t = function() {
                this.$body = a("body");
                this.dragulaInstance = null;
                this.dragTimeout = null;
            };

            t.prototype.init = function() {
                var self = this;

                a('[data-plugin="dragula"]').each(function() {
                    var $wrapper = a(this);
                    var containers = $wrapper.data("containers");
                    var containerElements = [];

                    if (containers) {
                        for (var i = 0; i < containers.length; i++) {
                            var element = a("#" + containers[i])[0];
                            if (element) containerElements.push(element);
                        }
                    } else {
                        containerElements = [$wrapper[0]];
                    }

                    var handleClass = $wrapper.data("handleclass");

                    // Destroy existing instance if it exists
                    if (self.dragulaInstance) {
                        self.dragulaInstance.destroy();
                    }

                    // Create optimized dragula instance
                    self.dragulaInstance = dragula(containerElements, {
                        moves: function(el, container, handle) {
                            if (handleClass) {
                                return handle.classList.contains(handleClass);
                            }
                            // Allow dragging on card but not on interactive elements
                            return !a(handle).closest('a, button, .btn, .dropdown-menu, input, textarea, select').length;
                        },
                        accepts: function(el, target, source, sibling) {
                            return a(target).hasClass('sales-item-wrp');
                        },
                        removeOnSpill: false,
                        revertOnSpill: true,
                        direction: 'vertical',
                        mirrorContainer: document.body
                    });

                    // Add optimized event handlers
                    self.dragulaInstance.on('drag', function(el, source) {
                        a(el).addClass('gu-transit-optimized');
                        a('body').addClass('dragging-leads');

                        // Disable text selection during drag
                        a('body').css('user-select', 'none');
                    });

                    self.dragulaInstance.on('dragend', function(el) {
                        a(el).removeClass('gu-transit-optimized');
                        a('body').removeClass('dragging-leads');

                        // Re-enable text selection
                        a('body').css('user-select', '');
                    });

                    self.dragulaInstance.on('over', function(el, container, source) {
                        a(container).addClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('out', function(el, container, source) {
                        a(container).removeClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('drop', function(el, target, source, sibling) {
                        // Clear any existing timeout
                        if (self.dragTimeout) {
                            clearTimeout(self.dragTimeout);
                        }

                        // Remove drag classes immediately for better UX
                        a(el).removeClass('gu-transit-optimized');
                        a(target).removeClass('drag-over-highlight');
                        a('body').removeClass('dragging-leads');
                        a('body').css('user-select', '');

                        // Get data efficiently
                        var leadId = a(el).attr('data-id');
                        var sourceId = a(source).attr('id');
                        var targetId = a(target).attr('id');
                        var stageId = a(target).attr('data-id');
                        var oldStatus = a(source).data('status');
                        var newStatus = a(target).data('status');
                        var pipelineId = '<?php echo e($pipeline->id); ?>';

                        // Update counts immediately for better UX
                        var sourceCount = a("#" + sourceId + " > div").length;
                        var targetCount = a("#" + targetId + " > div").length;

                        a("#" + sourceId).parent().find('.count').text(sourceCount);
                        a("#" + targetId).parent().find('.count').text(targetCount);

                        // Collect order efficiently
                        var order = [];
                        a("#" + targetId + " > div").each(function(index) {
                            var dataId = a(this).attr('data-id');
                            if (dataId) order.push(dataId);
                        });

                        // Add loading state
                        a(el).addClass('updating-lead');

                        // Debounced AJAX call
                        self.dragTimeout = setTimeout(function() {
                            a.ajax({
                                url: '<?php echo e(route('lead_stages.moveLeadToStage')); ?>',
                                type: 'POST',
                                data: {
                                    lead_id: leadId,
                                    stage_id: stageId,
                                    order: order,
                                    new_status: newStatus,
                                    old_status: oldStatus,
                                    pipeline_id: pipelineId,
                                    "_token": a('meta[name="csrf-token"]').attr('content')
                                },
                                success: function(data) {
                                    a(el).removeClass('updating-lead');
                                    if (data.status === 'success') {
                                        show_toastr('success', data.message, 'success');
                                    } else {
                                        show_toastr(data.status, data.message, data.status);
                                    }
                                },
                                error: function(xhr) {
                                    a(el).removeClass('updating-lead');
                                    var data = xhr.responseJSON || {};
                                    show_toastr('error', data.message || 'An error occurred', 'error');

                                    // Revert the move on error
                                    if (source !== target) {
                                        a(source).append(el);
                                        // Restore counts
                                        a("#" + sourceId).parent().find('.count').text(a("#" + sourceId + " > div").length);
                                        a("#" + targetId).parent().find('.count').text(a("#" + targetId + " > div").length);
                                    }
                                }
                            });
                        }, 100); // Small delay to prevent rapid fire requests
                    });
                });
            };

            a.Dragula = new t;
            a.Dragula.Constructor = t;
        }(window.jQuery),
        function(a) {
            "use strict";

            // Initialize dragula with proper timing
            a(document).ready(function() {
                // Small delay to ensure DOM is fully rendered
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

            // Reinitialize on dynamic content updates
            a(document).on('contentUpdated', function() {
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

        }(window.jQuery);

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '<?php echo e(__('No sources found for this lead.')); ?>';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }
    </script>
    <script>
        $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });
        
        // Search functionality for leads
        $('#lead-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            // Show/hide clear icon based on input value
            if (searchTerm.length > 0) {
                $('.search-icon').hide();
                $('.clear-icon').show();
            } else {
                $('.search-icon').show();
                $('.clear-icon').hide();
            }
            
            // Search through all lead cards
            $('.kanban-card').each(function() {
                var leadCard = $(this);
                var leadName = leadCard.find('.lead-title a').text().toLowerCase();
                var leadEmail = leadCard.find('.contact-item:nth-child(3) span').text().toLowerCase();
                var leadPhone = leadCard.find('.contact-item:nth-child(2) span').text().toLowerCase();
                
                // Check if search term matches any lead data
                var isMatch = leadName.includes(searchTerm) || 
                             leadEmail.includes(searchTerm) || 
                             leadPhone.includes(searchTerm);
                
                if (searchTerm === '' || isMatch) {
                    leadCard.show();
                } else {
                    leadCard.hide();
                }
            });
            
            // Update stage counts
            $('.kanban-col').each(function() {
                var stageColumn = $(this);
                var visibleLeads = stageColumn.find('.kanban-card:visible').length;
                stageColumn.find('.count').text(visibleLeads);
            });
        });
        
        // Search icon click handler
        $('.search-icon').on('click', function() {
            $('#lead-search').focus();
        });
        
        // Clear icon click handler
        $('.clear-icon').on('click', function() {
            $('#lead-search').val('').trigger('input');
            $('#lead-search').focus();
        });





        // Initialize Lead Filter System
        $(document).ready(function() {
            // Check if required libraries are loaded
            if (typeof $.fn.select2 === 'undefined') {
                console.error('Select2 is not loaded!');
                show_toastr('Error', 'Select2 library is not loaded. Please refresh the page.', 'error');
                return;
            }

            if (typeof flatpickr === 'undefined') {
                console.error('Flatpickr is not loaded!');
                show_toastr('Error', 'Flatpickr library is not loaded. Please refresh the page.', 'error');
                return;
            }

            console.log('✅ Select2 and Flatpickr libraries loaded successfully');

            let currentDateFilterType = ''; // 'created' or 'updated'
            let dateRangePicker;
            let select2Initialized = false;

            // Function to initialize Select2 filters
            function initializeSelect2Filters() {
                if (select2Initialized) {
                    return;
                }

                // Destroy existing instances first
                if ($('#assigned_to_filter').hasClass('select2-hidden-accessible')) {
                    $('#assigned_to_filter').select2('destroy');
                }
                if ($('#tags_filter').hasClass('select2-hidden-accessible')) {
                    $('#tags_filter').select2('destroy');
                }
                if ($('#lead_source_filter').hasClass('select2-hidden-accessible')) {
                    $('#lead_source_filter').select2('destroy');
                }

                // Initialize Select2 for all multi-select filters
                $('#assigned_to_filter').select2({
                    dropdownParent: $('#filterOffcanvasKanban'),
                    allowClear: false,
                    width: '100%',
                    minimumResultsForSearch: 10
                });

                $('#tags_filter').select2({
                    dropdownParent: $('#filterOffcanvasKanban'),
                    allowClear: false,
                    width: '100%',
                    minimumResultsForSearch: 10
                });

                $('#lead_source_filter').select2({
                    dropdownParent: $('#filterOffcanvasKanban'),
                    allowClear: false,
                    width: '100%',
                    minimumResultsForSearch: 10
                });

                select2Initialized = true;
            }

            // Initialize Select2 when offcanvas is shown
            $('#filterOffcanvasKanban').on('shown.bs.offcanvas', function() {
                setTimeout(function() {
                    initializeSelect2Filters();
                }, 100);
            });

            // Initialize Select2 when offcanvas is hidden
            $('#filterOffcanvasKanban').on('hidden.bs.offcanvas', function() {
                // Reset Select2 instances
                if ($('#assigned_to_filter').hasClass('select2-hidden-accessible')) {
                    $('#assigned_to_filter').select2('destroy');
                }
                if ($('#tags_filter').hasClass('select2-hidden-accessible')) {
                    $('#tags_filter').select2('destroy');
                }
                if ($('#lead_source_filter').hasClass('select2-hidden-accessible')) {
                    $('#lead_source_filter').select2('destroy');
                }
                select2Initialized = false;
            });

            // Initialize Flatpickr for the date range picker in nested off-canvas
            function initializeDateRangePicker() {
                if (dateRangePicker) {
                    dateRangePicker.destroy();
                }

                dateRangePicker = flatpickr("#date_range_picker", {
                    mode: "range",
                    dateFormat: "Y-m-d",
                    placeholder: "<?php echo e(__('Select date range')); ?>",
                    allowInput: false
                });
            }

            // Handle Created On filter change
            $('#created_filter').on('change', function() {
                const value = $(this).val();
                if (value === 'custom') {
                    currentDateFilterType = 'created';
                    initializeDateRangePicker();

                    // Set existing values if any
                    const existingFrom = $('#created_from').val();
                    const existingTo = $('#created_to').val();
                    if (existingFrom && existingTo) {
                        dateRangePicker.setDate([existingFrom, existingTo]);
                    }

                    // Open the nested off-canvas
                    const dateRangeOffcanvas = new bootstrap.Offcanvas(document.getElementById('dateRangeOffcanvas'));
                    dateRangeOffcanvas.show();
                } else {
                    $('#created_range_display').hide();
                    $('#created_from').val('');
                    $('#created_to').val('');
                    $('#created_range_text').val('');
                }
            });

            // Handle Updated On filter change
            $('#updated_filter').on('change', function() {
                const value = $(this).val();
                if (value === 'custom') {
                    currentDateFilterType = 'updated';
                    initializeDateRangePicker();

                    // Set existing values if any
                    const existingFrom = $('#updated_from').val();
                    const existingTo = $('#updated_to').val();
                    if (existingFrom && existingTo) {
                        dateRangePicker.setDate([existingFrom, existingTo]);
                    }

                    // Open the nested off-canvas
                    const dateRangeOffcanvas = new bootstrap.Offcanvas(document.getElementById('dateRangeOffcanvas'));
                    dateRangeOffcanvas.show();
                } else {
                    $('#updated_range_display').hide();
                    $('#updated_from').val('');
                    $('#updated_to').val('');
                    $('#updated_range_text').val('');
                }
            });

            // Handle edit date range buttons
            $('#edit_created_range').on('click', function() {
                currentDateFilterType = 'created';
                initializeDateRangePicker();

                const existingFrom = $('#created_from').val();
                const existingTo = $('#created_to').val();
                if (existingFrom && existingTo) {
                    dateRangePicker.setDate([existingFrom, existingTo]);
                }

                const dateRangeOffcanvas = new bootstrap.Offcanvas(document.getElementById('dateRangeOffcanvas'));
                dateRangeOffcanvas.show();
            });

            $('#edit_updated_range').on('click', function() {
                currentDateFilterType = 'updated';
                initializeDateRangePicker();

                const existingFrom = $('#updated_from').val();
                const existingTo = $('#updated_to').val();
                if (existingFrom && existingTo) {
                    dateRangePicker.setDate([existingFrom, existingTo]);
                }

                const dateRangeOffcanvas = new bootstrap.Offcanvas(document.getElementById('dateRangeOffcanvas'));
                dateRangeOffcanvas.show();
            });

            // Handle apply date range
            $('#apply_date_range').on('click', function() {
                const selectedDates = dateRangePicker.selectedDates;

                if (selectedDates.length === 2) {
                    const fromDate = flatpickr.formatDate(selectedDates[0], "Y-m-d");
                    const toDate = flatpickr.formatDate(selectedDates[1], "Y-m-d");
                    const rangeText = fromDate + ' to ' + toDate;

                    if (currentDateFilterType === 'created') {
                        $('#created_from').val(fromDate);
                        $('#created_to').val(toDate);
                        $('#created_range_text').val(rangeText);
                        $('#created_range_display').show();
                    } else if (currentDateFilterType === 'updated') {
                        $('#updated_from').val(fromDate);
                        $('#updated_to').val(toDate);
                        $('#updated_range_text').val(rangeText);
                        $('#updated_range_display').show();
                    }

                    // Close the date range off-canvas
                    const dateRangeOffcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('dateRangeOffcanvas'));
                    if (dateRangeOffcanvas) {
                        dateRangeOffcanvas.hide();
                    }

                    show_toastr('Success', 'Date range selected successfully', 'success');
                } else {
                    show_toastr('Error', 'Please select a valid date range', 'error');
                }
            });

            // jQuery events for Select2 filters
            $('#assigned_to_filter').on('change', function() {
                const selectedValues = $(this).val();
                console.log('Assigned To Filter Changed:', selectedValues);
            });

            $('#tags_filter').on('change', function() {
                const selectedValues = $(this).val();
                console.log('Tags Filter Changed:', selectedValues);
            });

            $('#lead_source_filter').on('change', function() {
                const selectedValues = $(this).val();
                console.log('Lead Source Filter Changed:', selectedValues);
            });
        });

        // Handle Create Label Modal
        $('#createLabelModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var leadId = button.data('lead-id');
            var pipelineId = button.data('pipeline-id');

            var modal = $(this);
            modal.find('#modal_lead_id').val(leadId);
            modal.find('#modal_pipeline_id').val(pipelineId);
        });

        // Handle Create Label Form Submission
        $('#createLabelForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var leadId = $('#modal_lead_id').val();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#createLabelModal').modal('hide');
                    show_toastr('success', response.message || 'Label created successfully!');

                    // Reload the page to show the new label
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errors = xhr.responseJSON.errors;
                        errorMessage = Object.values(errors).flat().join(', ');
                    }
                    show_toastr('error', errorMessage);
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Lead')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end ">
        <!-- Search Bar -->
        <div class="search-container me-2">
            <input type="text" 
                   id="lead-search" 
                   class="form-control search-input" 
                   placeholder="<?php echo e(__('Search leads...')); ?>" 
                   autocomplete="off">
            <i class="fas fa-search search-icon"></i>
            <i class="fas fa-times clear-icon" style="display: none;"></i>
        </div>
        
        <?php echo e(Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'btn btn-sm'])); ?>

        <?php echo e(Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['class' => 'form-control select me-2', 'id' => 'default_pipeline_id'])); ?>

        <?php echo e(Form::close()); ?>


        <a href="<?php echo e(route('leads.list')); ?>"
            data-size="lg"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('List View')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-list" style="font-size:16px;"></i>
        </a>

        <!-- Import Lead -->
        <a href="#"
            data-size="md"
            data-bs-toggle="tooltip"
            data-ajax-popup="true"
            data-bs-placement="bottom"
            title="<?php echo e(__('Import')); ?>"
            data-url="<?php echo e(route('leads.import')); ?>"
            data-ajax-popup="true"
            data-title="<?php echo e(__('Import Lead CSV file')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="fas fa-file-import" style="font-size:16px;"></i>
        </a>
        <!-- Filter Leads -->
        <a href="#"
            data-bs-toggle="offcanvas"
            data-bs-target="#filterOffcanvasKanban"
            aria-controls="filterOffcanvasKanban"
            data-bs-placement="bottom"
            title="<?php echo e(__('Filter Leads')); ?>"
            id="filter-btn-kanban"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px; border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white; box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-filter" style="font-size:16px;"></i>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none" id="filter-active-badge-kanban"></span>
        </a>
        <!-- Export Leads -->
        <a href="<?php echo e(route('leads.export')); ?>"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('Export')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="fas fa-file-export" style="font-size:16px;"></i>
        </a>
        <!-- Create New Lead -->
        <a href="#"
            data-size="lg"
            data-url="<?php echo e(route('leads.create')); ?>"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('Create New Lead')); ?>"
            data-title="<?php echo e(__('Create Lead')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;""
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-plus" style="font-size:16px;"></i>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <?php
                $lead_stages = $pipeline->leadStages;
                $json = [];
                foreach ($lead_stages as $lead_stage) {
                    $json[] = 'task-list-' . $lead_stage->id;
                }
            ?>
            <div class="kanban-wrapper horizontal-scroll-cards" data-containers='<?php echo json_encode($json); ?>' data-plugin="dragula">
                <?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php ($leads = $lead_stage->lead()); ?>
                    <div class="kanban-col crm-sales-card">
                        <div class="kanban-header">
                            <h4><?php echo e($lead_stage->name); ?></h4>
                            <span class="count f-w-600"><?php echo e(count($leads)); ?></span>
                        </div>
                        <div class="sales-item-wrp" id="task-list-<?php echo e($lead_stage->id); ?>" data-id="<?php echo e($lead_stage->id); ?>" data-status="<?php echo e($lead_stage->id); ?>">
                            <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="kanban-card sales-item" data-id="<?php echo e($lead->id); ?>" style="border-top: 4px solid #2e7d32;">
                                    <div class="card-top">
                                        <div class="lead-title">
                                            <a href="<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view lead')): ?><?php if($lead->is_active): ?><?php echo e(route('leads.show', $lead->id)); ?><?php else: ?>#<?php endif; ?> <?php else: ?>#<?php endif; ?>" class="dashboard-link">
                                            <i class="fas fa-user me-2"></i><?php echo e($lead->name); ?>

                                            </a>
                                        </div>
                                        <?php if(Auth::user()->type != 'client'): ?>
                                            <div class="btn-group card-option">
                                                <button type="button" class="btn p-0 border-0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <div class="dropdown-menu icon-dropdown dropdown-menu-end">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                        <a href="#" data-size="md" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/labels')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Add Labels')); ?>">
                                                            <i class="ti ti-bookmark"></i>
                                                            <span><?php echo e(__('Labels')); ?></span>
                                                        </a>
                                                        <a href="#" data-size="lg" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/edit')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Edit Lead')); ?>">
                                                            <i class="ti ti-pencil"></i>
                                                            <span><?php echo e(__('Edit')); ?></span>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete lead')): ?>
                                                        <?php echo Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['leads.destroy', $lead->id],
                                                            'id' => 'delete-form-' . $lead->id,
                                                        ]); ?>

                                                        <a href="#" class="dropdown-item bs-pass-para">
                                                            <i class="ti ti-trash"></i>
                                                            <span> <?php echo e(__('Delete')); ?> </span>
                                                        </a>
                                                        <?php echo Form::close(); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="badge-wrp">
                                        <?php ($labels = $lead->labels()); ?>
                                        <?php if($labels): ?>
                                            <?php $__currentLoopData = $labels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="kanban-label bg-light-<?php echo e($label->color); ?>"><?php echo e($label->name); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                    <?php
                                    $products = $lead->products();
                                    $sources = $lead->sources();
                                    ?>
                                    <div class="contact-info position-relative">
                                        <button type="button" class="position-absolute top-0 end-0 d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1 bg-light" style="margin-right: 17px;" data-bs-toggle="tooltip" title="<?php echo e(__('Source')); ?>" onclick="openSourcesModal(<?php echo e(json_encode($sources)); ?>)">
                                            <i class="f-16 ti ti-social"></i>
                                            <!-- <?php echo e(count($sources)); ?> -->
                                        </button>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-phone-volume me-1"></i>
                                            <span><?php echo e($lead->phone); ?></span>
                                        </div>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-envelope me-1"></i>
                                            <span><?php echo e($lead->email); ?></span>
                                        </div>
                                    </div>
                                    <div class="card-bottom">
                                        <div class="communication-buttons">
                                            <button class="communication-btn call" data-bs-toggle="tooltip" title="Call">
                                                <i class="fas fa-phone-alt"></i>
                                            </button>
                                            <button class="communication-btn whatsapp" data-bs-toggle="tooltip" title="WhatsApp">
                                                <i class="fab fa-whatsapp"></i>
                                            </button>
                                            <button class="communication-btn email" data-bs-toggle="tooltip" title="Email" onclick="openCommunicationModal(<?php echo e(json_encode(['name' => $lead->name, 'phone' => $lead->phone, 'email' => $lead->email])); ?>)">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="communication-btn activity" data-bs-toggle="modal" data-bs-target="#activityModal-<?php echo e($lead->id); ?>" title="Activity">
                                                <i class="fas fa-stream"></i>
                                            </button>
                                        </div>
                                        <div class="user-group">
                                            <?php $__currentLoopData = $lead->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <i class="fas fa-user-circle" data-bs-toggle="tooltip" title="<?php echo e($user->name); ?>"></i>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

<!-- Communication Modal -->
<div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-comm-modal">
            <div class="modal-header">
                <div class="d-flex align-items-center gap-3">
                    <div id="comm-lead-avatar" class="comm-lead-avatar"></div>
                    <div>
                        <h5 class="modal-title mb-0" id="communication-modal-label"></h5>
                        <div class="comm-lead-name" id="comm-lead-name"></div>
                        <div class="comm-lead-contact text-muted small" id="comm-lead-contact"></div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="communication-options-list">
                    <a href="#" class="communication-option-item" id="default-email-option">
                        <i class="fas fa-envelope-open-text"></i>
                        <span>Default Email App</span>
                    </a>
                    <a href="#" class="communication-option-item" id="cloud-email-option">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Cloud Email Service</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sources Modal -->
<div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sources-modal-label"><?php echo e(__('Lead Sources')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="list-group" id="sources-list">
                    <!-- Sources will be dynamically inserted here -->
                </ul>
            </div>
        </div>
    </div>
</div>

<?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php ($leads = $lead_stage->lead()); ?>
    <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Activity Modal for this lead -->
        <div class="modal fade" id="activityModal-<?php echo e($lead->id); ?>" tabindex="-1" aria-labelledby="activityModalLabel-<?php echo e($lead->id); ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activityModalLabel-<?php echo e($lead->id); ?>"><?php echo e(__('Activity')); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row leads-scroll">
                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                <?php if(!$lead->activities->isEmpty()): ?>
                                    <?php $__currentLoopData = $lead->activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="list-group-item card mb-3">
                                            <div class="row align-items-center justify-content-between">
                                                <div class="col-auto mb-3 mb-sm-0">
                                                    <div class="d-flex align-items-center">
                                                        <div class="theme-avtar bg-primary badge">
                                                            <i class="ti <?php echo e($activity->logIcon()); ?>"></i>
                                                        </div>
                                                        <div class="ms-3">
                                                            <span class="text-dark text-sm"><?php echo e(__($activity->log_type)); ?></span>
                                                            <h6 class="m-0"><?php echo $activity->getLeadRemark(); ?></h6>
                                                            <small class="text-muted"><?php echo e($activity->created_at->diffForHumans()); ?></small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <li class="text-center py-4">No activity found yet.</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- Filter Off-canvas for Kanban -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvasKanban" aria-labelledby="filterOffcanvasKanbanLabel" style="width: 400px;">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="filterOffcanvasKanbanLabel"><?php echo e(__('Filter Leads')); ?></h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <?php echo e(Form::open(['route' => 'leads.index', 'method' => 'GET', 'id' => 'lead-filter-form-kanban'])); ?>


            <!-- Assigned To Filter -->
            <div class="mb-3">
                <?php echo e(Form::label('assigned_to', __('Assigned To'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('assigned_to[]', $assignedUsers, request('assigned_to'), ['class' => 'form-control', 'id' => 'assigned_to_filter', 'multiple' => true])); ?>

            </div>

            <!-- Tag/Label Filter -->
            <div class="mb-3">
                <?php echo e(Form::label('tags', __('Tags'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('tags[]', $availableLabels, request('tags'), ['class' => 'form-control', 'id' => 'tags_filter', 'multiple' => true])); ?>

            </div>

            <!-- Created On Filter -->
            <div class="mb-3">
                <?php echo e(Form::label('created_filter', __('Created On'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('created_filter', [
                    'today' => __('Today'),
                    'last_week' => __('Last Week'),
                    'this_month' => __('This Month'),
                    'this_year' => __('This Year'),
                    'custom' => __('Custom Range')
                ], request('created_filter'), ['class' => 'form-control', 'placeholder' => __('Select Period'), 'id' => 'created_filter'])); ?>

            </div>

            <!-- Created Date Range Display -->
            <div class="mb-3" id="created_range_display" style="display: <?php echo e(request('created_filter') == 'custom' && request('created_from') && request('created_to') ? 'block' : 'none'); ?>;">
                <label class="form-label"><?php echo e(__('Selected Created Date Range')); ?></label>
                <div class="input-group">
                    <input type="text" id="created_range_text" class="form-control" readonly value="<?php echo e(request('created_from') && request('created_to') ? request('created_from') . ' to ' . request('created_to') : ''); ?>">
                    <button type="button" class="btn btn-outline-secondary" id="edit_created_range">
                        <i class="ti ti-edit"></i>
                    </button>
                </div>
                <input type="hidden" name="created_from" id="created_from" value="<?php echo e(request('created_from')); ?>">
                <input type="hidden" name="created_to" id="created_to" value="<?php echo e(request('created_to')); ?>">
            </div>

            <!-- Updated On Filter -->
            <div class="mb-3">
                <?php echo e(Form::label('updated_filter', __('Updated On'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('updated_filter', [
                    'today' => __('Today'),
                    'last_week' => __('Last Week'),
                    'this_month' => __('This Month'),
                    'this_year' => __('This Year'),
                    'custom' => __('Custom Range')
                ], request('updated_filter'), ['class' => 'form-control', 'placeholder' => __('Select Period'), 'id' => 'updated_filter'])); ?>

            </div>

            <!-- Updated Date Range Display -->
            <div class="mb-3" id="updated_range_display" style="display: <?php echo e(request('updated_filter') == 'custom' && request('updated_from') && request('updated_to') ? 'block' : 'none'); ?>;">
                <label class="form-label"><?php echo e(__('Selected Updated Date Range')); ?></label>
                <div class="input-group">
                    <input type="text" id="updated_range_text" class="form-control" readonly value="<?php echo e(request('updated_from') && request('updated_to') ? request('updated_from') . ' to ' . request('updated_to') : ''); ?>">
                    <button type="button" class="btn btn-outline-secondary" id="edit_updated_range">
                        <i class="ti ti-edit"></i>
                    </button>
                </div>
                <input type="hidden" name="updated_from" id="updated_from" value="<?php echo e(request('updated_from')); ?>">
                <input type="hidden" name="updated_to" id="updated_to" value="<?php echo e(request('updated_to')); ?>">
            </div>

            <!-- Lead Source Filter -->
            <div class="mb-3">
                <?php echo e(Form::label('lead_source', __('Lead Source'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('lead_source[]', $leadSources, request('lead_source'), ['class' => 'form-control', 'id' => 'lead_source_filter', 'multiple' => true])); ?>

            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="ti ti-search me-1"></i><?php echo e(__('Apply Filter')); ?>

                </button>
                <a href="<?php echo e(route('leads.index')); ?>" class="btn btn-secondary">
                    <i class="ti ti-refresh me-1"></i><?php echo e(__('Clear Filters')); ?>

                </a>
            </div>

            <?php echo e(Form::close()); ?>

        </div>
    </div>

    <script>
        // Handle filter form submission
        $('#lead-filter-form-kanban').on('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            $('.kanban-wrapper').addClass('loading');
            
            // Get form data
            var formData = $(this).serialize();
            
            // Add pipeline ID
            formData += '&pipeline_id=<?php echo e($pipeline->id); ?>';
            
            // Make AJAX request
            $.ajax({
                url: '<?php echo e(route("leads.getFilterData")); ?>',
                type: 'GET',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        updateKanbanWithFilteredLeads(response.data);
                        show_toastr('Success', 'Filters applied successfully', 'success');
                        
                        // Show filter active badge
                        $('#filter-active-badge-kanban').removeClass('d-none');
                    } else {
                        show_toastr('Error', response.message || 'An error occurred', 'error');
                    }
                },
                error: function(xhr) {
                    show_toastr('Error', 'An error occurred while applying filters', 'error');
                },
                complete: function() {
                    $('.kanban-wrapper').removeClass('loading');
                }
            });
        });

        // Function to update kanban with filtered leads
        function updateKanbanWithFilteredLeads(leads) {
            // Group leads by stage
            var leadsByStage = {};
            leads.forEach(function(lead) {
                if (!leadsByStage[lead.stage_id]) {
                    leadsByStage[lead.stage_id] = [];
                }
                leadsByStage[lead.stage_id].push(lead);
            });

            // Update each stage column
            $('.kanban-col').each(function() {
                var stageId = $(this).find('.sales-item-wrp').data('id');
                var stageLeads = leadsByStage[stageId] || [];
                
                // Update count
                $(this).find('.count').text(stageLeads.length);
                
                // Clear existing leads
                $(this).find('.sales-item-wrp').empty();
                
                // Add filtered leads
                stageLeads.forEach(function(lead) {
                    var leadCard = createLeadCard(lead);
                    $(this).find('.sales-item-wrp').append(leadCard);
                }.bind(this));
            });
        }

        // Function to create lead card HTML
        function createLeadCard(lead) {
            var cardHtml = `
                <div class="kanban-card sales-item" data-id="${lead.id}" style="border-top: 4px solid #2e7d32;">
                    <div class="card-top">
                        <div class="lead-title">
                            <a href="/leads/${lead.id}" class="dashboard-link">
                                <i class="fas fa-user me-2"></i>${lead.name}
                            </a>
                        </div>
                        <div class="btn-group card-option">
                            <button type="button" class="btn p-0 border-0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="ti ti-dots-vertical"></i>
                            </button>
                            <div class="dropdown-menu icon-dropdown dropdown-menu-end">
                                <a href="#" data-size="md" data-url="/leads/${lead.id}/labels" data-ajax-popup="true" class="dropdown-item">
                                    <i class="ti ti-bookmark"></i>
                                    <span><?php echo e(__('Labels')); ?></span>
                                </a>
                                <a href="#" data-size="lg" data-url="/leads/${lead.id}/edit" data-ajax-popup="true" class="dropdown-item">
                                    <i class="ti ti-pencil"></i>
                                    <span><?php echo e(__('Edit')); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="badge-wrp">
                        ${lead.labels ? lead.labels.map(label => `<span class="kanban-label bg-light-${label.color}">${label.name}</span>`).join('') : ''}
                    </div>
                    <div class="contact-info position-relative">
                        <button type="button" class="position-absolute top-0 end-0 d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1 bg-light" style="margin-right: 17px;" data-bs-toggle="tooltip" title="<?php echo e(__('Source')); ?>" onclick="openSourcesModal(${JSON.stringify(lead.sources || [])})">
                            <i class="f-16 ti ti-social"></i>
                        </button>
                        <div class="contact-item text-success">
                            <i class="fas fa-phone-volume me-1"></i>
                            <span>${lead.phone || ''}</span>
                        </div>  
                        <div class="contact-item text-success">
                            <i class="fas fa-envelope me-1"></i>
                            <span>${lead.email || ''}</span>
                        </div>
                    </div>
                    <div class="card-bottom">
                        <div class="communication-buttons">
                            <button class="communication-btn call" data-bs-toggle="tooltip" title="Call">
                                <i class="fas fa-phone-alt"></i>
                            </button>
                            <button class="communication-btn whatsapp" data-bs-toggle="tooltip" title="WhatsApp">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button class="communication-btn email" data-bs-toggle="tooltip" title="Email" onclick="openCommunicationModal(${JSON.stringify({name: lead.name, phone: lead.phone, email: lead.email})})">
                                <i class="fas fa-envelope"></i>
                            </button>
                            <button class="communication-btn activity" data-bs-toggle="modal" data-bs-target="#activityModal-${lead.id}" title="Activity">
                                <i class="fas fa-stream"></i>
                            </button>
                        </div>
                        <div class="user-group">
                            ${lead.users ? lead.users.map(user => `<i class="fas fa-user-circle" data-bs-toggle="tooltip" title="${user.name}"></i>`).join('') : ''}
                        </div>
                    </div>
                </div>
            `;
            return cardHtml;
        }
    </script>

    <!-- Date Range Off-canvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="dateRangeOffcanvas" aria-labelledby="dateRangeOffcanvasLabel" style="width: 350px;">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="dateRangeOffcanvasLabel"><?php echo e(__('Select Date Range')); ?></h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="mb-3">
                <label class="form-label"><?php echo e(__('Date Range')); ?></label>
                <input type="text" id="date_range_picker" class="form-control" placeholder="<?php echo e(__('Select date range')); ?>" readonly>
            </div>
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary" id="apply_date_range">
                    <i class="ti ti-check me-1"></i><?php echo e(__('Apply Range')); ?>

                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- Date Range Off-canvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="dateRangeOffcanvas" aria-labelledby="dateRangeOffcanvasLabel" style="width: 350px;">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="dateRangeOffcanvasLabel"><?php echo e(__('Select Date Range')); ?></h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="mb-3">
                <label class="form-label"><?php echo e(__('Date Range')); ?></label>
                <input type="text" id="date_range_picker" class="form-control" placeholder="<?php echo e(__('Select date range')); ?>" readonly>
            </div>
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary" id="apply_date_range">
                    <i class="ti ti-check me-1"></i><?php echo e(__('Apply Range')); ?>

                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="offcanvas">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                </button>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<script>
    // Modern openCommunicationModal function
    function openCommunicationModal(lead) {
        const modalEl = document.getElementById('communication-modal');
        const modal = new bootstrap.Modal(modalEl);
        const title = document.getElementById('communication-modal-label');
        const name = document.getElementById('comm-lead-name');
        const contact = document.getElementById('comm-lead-contact');
        const avatar = document.getElementById('comm-lead-avatar');

        // Set modal title
        title.textContent = 'Contact Lead';
        name.textContent = lead.name || '';
        contact.innerHTML =
            (lead.phone ? `<i class='fas fa-phone-alt'></i> ${lead.phone}` : '') +
            (lead.email ? ` &nbsp; <i class='fas fa-envelope'></i> ${lead.email}` : '');
        // Avatar: initials
        if (lead.name) {
            const initials = lead.name.split(' ').map(w => w[0]).join('').substring(0,2).toUpperCase();
            avatar.textContent = initials;
        } else {
            avatar.textContent = '?';
        }
        // WhatsApp
        // const whatsapp = document.getElementById('whatsapp-option');
        // if (lead.phone) {
        //     whatsapp.href = `https://wa.me/${lead.phone.replace(/[^\d]/g, '')}`;
        //     whatsapp.classList.remove('disabled');
        // } else {
        //     whatsapp.href = '#';
        //     whatsapp.classList.add('disabled');
        // }
        // Email (default app)
        const email = document.getElementById('default-email-option');
        if (lead.email) {
            email.href = `mailto:${lead.email}`;
            email.classList.remove('disabled');
        } else {
            email.href = '#';
            email.classList.add('disabled');
        }
        // Cloud email (customize as needed)
        const cloud = document.getElementById('cloud-email-option');
        if (lead.email) {
            cloud.href = `mailto:${lead.email}`;
            cloud.classList.remove('disabled');
        } else {
            cloud.href = '#';
            cloud.classList.add('disabled');
        }
        // SMS
        // const sms = document.getElementById('sms-option');
        // if (lead.phone) {
        //     sms.href = `sms:${lead.phone}`;
        //     sms.classList.remove('disabled');
        // } else {
        //     sms.href = '#';
        //     sms.classList.add('disabled');
        // }
        modal.show();
    }
            // Attach openCommunicationModal to each card's SMS and Email button using event delegation
        document.addEventListener('DOMContentLoaded', function() {
            document.body.addEventListener('click', function(e) {
                // Email button
                if (e.target.closest('.kanban-card .communication-btn.email[onclick^="openCommunicationModal"]')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.email');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    const email = card.querySelector('.contact-item:nth-child(3) span')?.textContent.trim();
                    openCommunicationModal({name, phone, email});
                }
                // Call button
                if (e.target.closest('.kanban-card .communication-btn.call')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.call');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    
                    // Open phone dialer if phone number exists
                    if (phone) {
                        const cleanPhone = phone.replace(/[^\d]/g, '');
                        const callUrl = `tel:${cleanPhone}`;
                        window.open(callUrl, '_self');
                    } else {
                        show_toastr('error', 'No phone number available for call', 'error');
                    }
                }
                
                // WhatsApp button
                if (e.target.closest('.kanban-card .communication-btn.whatsapp')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.whatsapp');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    
                    // Open WhatsApp directly if phone number exists
                    if (phone) {
                        const cleanPhone = phone.replace(/[^\d]/g, '');
                        const whatsappUrl = `https://wa.me/${cleanPhone}?text=Hi ${name}, I hope you're doing well. I'd like to discuss a business opportunity with you.`;
                        window.open(whatsappUrl, '_blank');
                    } else {
                        show_toastr('error', 'No phone number available for WhatsApp', 'error');
                    }
                }
            });

        // --- Modal backdrop/blur fix ---
        // Remove any leftover modal-backdrop and reset body styles when any modal is hidden
        document.querySelectorAll('.modal').forEach(function(modal) {
            modal.addEventListener('hidden.bs.modal', function () {
                // Remove all modal-backdrop elements
                document.querySelectorAll('.modal-backdrop').forEach(function(backdrop) {
                    backdrop.parentNode.removeChild(backdrop);
                });
                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
                // Remove any inline style added by Bootstrap
                document.body.style = '';
            });
        });
    });
</script>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/leads/index.blade.php ENDPATH**/ ?>