@extends('layouts.admin')
@section('page-title')
    {{__('Manage Pipelines')}}
@endsection

@push('css-page')
    <style>
        .pipeline-stats {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .stat-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .stat-badge.lead-stages {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-badge.deal-stages {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-badge.leads-count {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-badge.deals-count {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .pipeline-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .pipeline-created {
            color: #718096;
            font-size: 0.875rem;
            margin-bottom: 10px;
        }
        .table td {
            vertical-align: middle;
            padding: 1rem 0.75rem;
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .btn-action {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
    </style>
@endpush

@push('script-page')
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Pipelines')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="#" data-size="md" data-url="{{ route('pipelines.create') }}" data-ajax-popup="true"
           data-bs-toggle="tooltip" title="{{__('Create New Pipeline')}}"
           class="btn btn-sm btn-primary">
            <i class="ti ti-plus me-1"></i>{{__('Create Pipeline')}}
        </a>
    </div>
@endsection

@section('content')
    @include('layouts.crm_horizontal_menu')
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">{{__('Pipeline Management')}}</h5>
            <small class="text-muted">{{__('Manage your sales and lead pipelines')}}</small>
        </div>
        <div class="card-body table-border-style">
            <div class="table-responsive">
                <table class="table datatable">
                    <thead>
                        <tr>
                            <th>{{__('Pipeline Details')}}</th>
                            <th>{{__('Stages & Statistics')}}</th>
                            <th width="150px">{{__('Actions')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($pipelines as $pipeline)
                            @php
                                $leadStagesCount = $pipeline->leadStages ? $pipeline->leadStages->count() : 0;
                                $dealStagesCount = $pipeline->stages ? $pipeline->stages->count() : 0;

                                // Count leads in this pipeline
                                $leadsCount = \App\Models\Lead::where('pipeline_id', $pipeline->id)
                                    ->where('created_by', \Auth::user()->creatorId())
                                    ->where('is_converted', 0)
                                    ->count();

                                // Count deals in this pipeline
                                $dealsCount = \App\Models\Deal::where('pipeline_id', $pipeline->id)
                                    ->where('created_by', \Auth::user()->creatorId())
                                    ->count();
                            @endphp
                            <tr>
                                <td>
                                    <div class="pipeline-name">{{ $pipeline->name }}</div>
                                    <div class="pipeline-created">
                                        <i class="ti ti-calendar me-1"></i>
                                        {{__('Created')}} {{ $pipeline->created_at->format('M d, Y') }}
                                    </div>
                                </td>
                                <td>
                                    <div class="pipeline-stats">
                                        <span class="stat-badge lead-stages">
                                            <i class="ti ti-flag"></i>
                                            {{ $leadStagesCount }} {{__('Lead Stages')}}
                                        </span>
                                        <span class="stat-badge deal-stages">
                                            <i class="ti ti-target"></i>
                                            {{ $dealStagesCount }} {{__('Deal Stages')}}
                                        </span>
                                        <span class="stat-badge leads-count">
                                            <i class="ti ti-users"></i>
                                            {{ $leadsCount }} {{__('Leads')}}
                                        </span>
                                        <span class="stat-badge deals-count">
                                            <i class="ti ti-briefcase"></i>
                                            {{ $dealsCount }} {{__('Deals')}}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        @can('edit pipeline')
                                            <a href="#" class="btn btn-sm btn-info btn-action"
                                               data-url="{{ URL::to('pipelines/'.$pipeline->id.'/edit') }}"
                                               data-ajax-popup="true" data-size="md"
                                               data-bs-toggle="tooltip" title="{{__('Edit Pipeline')}}"
                                               data-title="{{__('Edit Pipeline')}}">
                                                <i class="ti ti-pencil"></i>
                                            </a>
                                        @endcan
                                        @if(count($pipelines) > 1)
                                            @can('delete pipeline')
                                                {!! Form::open(['method' => 'DELETE', 'route' => ['pipelines.destroy', $pipeline->id], 'class' => 'd-inline']) !!}
                                                    <a href="#" class="btn btn-sm btn-danger btn-action bs-pass-para"
                                                       data-bs-toggle="tooltip" title="{{__('Delete Pipeline')}}">
                                                        <i class="ti ti-trash"></i>
                                                    </a>
                                                {!! Form::close() !!}
                                            @endcan
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
