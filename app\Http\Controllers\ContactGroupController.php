<?php

namespace App\Http\Controllers;

use App\Models\ContactGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ContactGroupController extends Controller
{
    /**
     * Display a listing of contact groups
     */
    public function index()
    {
        $contactGroups = ContactGroup::where('created_by', Auth::user()->creatorId())
            ->withCount('leads')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('contact-groups.index', compact('contactGroups'));
    }

    /**
     * Store a newly created contact group
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'selected_contacts' => 'nullable|array',
            'selected_contacts.*' => 'required|string',
            'existing_group_id' => 'nullable|exists:contact_groups,id'
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $contactGroup = null;

            // Check if using existing group or creating new one
            if ($request->filled('existing_group_id')) {
                $contactGroup = ContactGroup::where('id', $request->existing_group_id)
                    ->where('created_by', Auth::user()->creatorId())
                    ->first();

                if (!$contactGroup) {
                    throw new \Exception('Selected contact group not found');
                }
            } else {
                // Create new contact group
                $contactGroup = ContactGroup::create([
                    'name' => $request->name,
                    'description' => $request->description,
                    'created_by' => Auth::user()->creatorId()
                ]);
            }

            // Add selected leads to the group
            if ($request->filled('selected_contacts')) {
                $addedCount = 0;
                foreach ($request->selected_contacts as $contactData) {
                    $parts = explode(':', $contactData);
                    if (count($parts) === 2) {
                        $contactId = $parts[0];
                        $contactType = $parts[1];

                        // Only process leads for now
                        if ($contactType === 'Lead') {
                            $lead = \App\Models\Lead::find($contactId);
                            if ($lead && !$lead->contact_group_id) {
                                $lead->update(['contact_group_id' => $contactGroup->id]);
                                $addedCount++;
                            }
                        }
                    }
                }

                Log::info('Contact group operation completed', [
                    'group_id' => $contactGroup->id,
                    'group_name' => $contactGroup->name,
                    'leads_added' => $addedCount,
                    'user_id' => Auth::id()
                ]);
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Contact group created successfully!',
                    'group' => $contactGroup
                ]);
            }

            return redirect()->route('contact-groups.index')
                ->with('success', 'Contact group created successfully!');

        } catch (\Exception $e) {
            Log::error('Error creating contact group', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating contact group: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error creating contact group: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get all contact groups for dropdown
     */
    public function getContactGroups()
    {
        $groups = ContactGroup::where('created_by', Auth::user()->creatorId())
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'groups' => $groups
        ]);
    }

    /**
     * Get members of a contact group
     */
    public function getGroupMembers($id)
    {
        try {
            $contactGroup = ContactGroup::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$contactGroup) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contact group not found'
                ], 404);
            }

            $leads = \App\Models\Lead::where('contact_group_id', $id)->get();

            $memberDetails = [];
            foreach ($leads as $lead) {
                $memberDetails[] = [
                    'id' => $lead->id,
                    'name' => $lead->name,
                    'email' => $lead->email ?? '',
                    'phone' => $lead->phone ?? '',
                    'type' => 'Lead'
                ];
            }

            return response()->json([
                'success' => true,
                'members' => $memberDetails
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching group members', [
                'error' => $e->getMessage(),
                'group_id' => $id,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error fetching group members'
            ], 500);
        }
    }

    /**
     * Remove a lead from contact group
     */
    public function removeLead(Request $request, $groupId, $leadId)
    {
        try {
            $contactGroup = ContactGroup::where('id', $groupId)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$contactGroup) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contact group not found'
                ], 404);
            }

            $lead = \App\Models\Lead::find($leadId);
            if ($lead && $lead->contact_group_id == $groupId) {
                $lead->update(['contact_group_id' => null]);

                return response()->json([
                    'success' => true,
                    'message' => 'Lead removed from contact group successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Lead not found in this contact group'
            ], 404);

        } catch (\Exception $e) {
            Log::error('Error removing lead from contact group', [
                'error' => $e->getMessage(),
                'group_id' => $groupId,
                'lead_id' => $leadId,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error removing lead from contact group'
            ], 500);
        }
    }

    /**
     * Remove contact group
     */
    public function destroy($id)
    {
        try {
            $contactGroup = ContactGroup::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$contactGroup) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contact group not found'
                ], 404);
            }

            // Remove all leads from this group before deleting
            \App\Models\Lead::where('contact_group_id', $id)->update(['contact_group_id' => null]);

            $contactGroup->delete();

            return response()->json([
                'success' => true,
                'message' => 'Contact group deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting contact group', [
                'error' => $e->getMessage(),
                'group_id' => $id,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error deleting contact group'
            ], 500);
        }
    }
}
