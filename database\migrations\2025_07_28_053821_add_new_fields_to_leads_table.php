<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if columns already exist to avoid duplicate column errors
        if (!Schema::hasColumn('leads', 'date_of_birth')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->date('date_of_birth')->nullable()->after('phone');
            });
        }
        
        if (!Schema::hasColumn('leads', 'type')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->enum('type', ['new', 'existing', 'prospect', 'qualified', 'unqualified'])->nullable()->after('date_of_birth');
            });
        }
        
        if (!Schema::hasColumn('leads', 'status')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->enum('status', ['active', 'inactive', 'pending', 'completed', 'cancelled'])->default('active')->after('type');
            });
        }
        
        if (!Schema::hasColumn('leads', 'opportunity_info')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->string('opportunity_info')->nullable()->after('status');
            });
        }
        
        if (!Schema::hasColumn('leads', 'opportunity_description')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->text('opportunity_description')->nullable()->after('opportunity_info');
            });
        }
        
        if (!Schema::hasColumn('leads', 'opportunity_source')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->enum('opportunity_source', ['website', 'referral', 'social_media', 'email', 'phone', 'advertisement', 'other'])->nullable()->after('opportunity_description');
            });
        }
        
        if (!Schema::hasColumn('leads', 'lead_value')) {
            Schema::table('leads', function (Blueprint $table) {
                $table->decimal('lead_value', 15, 2)->nullable()->after('opportunity_source');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'date_of_birth',
                'type',
                'status',
                'opportunity_info',
                'opportunity_description',
                'opportunity_source',
                'lead_value'
            ]);
        });
    }
};
