<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Make pipeline_id and stage_id nullable to allow contacts without pipeline assignment
            if (Schema::hasColumn('leads', 'pipeline_id')) {
                $table->integer('pipeline_id')->nullable()->change();
            }
            if (Schema::hasColumn('leads', 'stage_id')) {
                $table->integer('stage_id')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Revert pipeline_id and stage_id to not nullable
            if (Schema::hasColumn('leads', 'pipeline_id')) {
                $table->integer('pipeline_id')->nullable(false)->change();
            }
            if (Schema::hasColumn('leads', 'stage_id')) {
                $table->integer('stage_id')->nullable(false)->change();
            }
        });
    }
};
