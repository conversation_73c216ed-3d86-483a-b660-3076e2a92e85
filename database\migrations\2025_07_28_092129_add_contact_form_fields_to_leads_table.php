<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Personal information fields
            if (!Schema::hasColumn('leads', 'first_name')) {
                $table->string('first_name')->nullable()->after('name');
            }
            if (!Schema::hasColumn('leads', 'last_name')) {
                $table->string('last_name')->nullable()->after('first_name');
            }
            if (!Schema::hasColumn('leads', 'contact_type')) {
                $table->string('contact_type')->default('Lead')->after('last_name');
            }
            if (!Schema::hasColumn('leads', 'tags')) {
                $table->text('tags')->nullable()->after('contact_type');
            }
            if (!Schema::hasColumn('leads', 'postal_code')) {
                $table->string('postal_code')->nullable()->after('tags');
            }
            if (!Schema::hasColumn('leads', 'city')) {
                $table->string('city')->nullable()->after('postal_code');
            }
            if (!Schema::hasColumn('leads', 'state')) {
                $table->string('state')->nullable()->after('city');
            }
            if (!Schema::hasColumn('leads', 'country')) {
                $table->string('country')->nullable()->after('state');
            }

            // Business information fields
            if (!Schema::hasColumn('leads', 'business_name')) {
                $table->string('business_name')->nullable()->after('country');
            }
            if (!Schema::hasColumn('leads', 'business_gst')) {
                $table->string('business_gst')->nullable()->after('business_name');
            }
            if (!Schema::hasColumn('leads', 'business_state')) {
                $table->string('business_state')->nullable()->after('business_gst');
            }
            if (!Schema::hasColumn('leads', 'business_postal_code')) {
                $table->string('business_postal_code')->nullable()->after('business_state');
            }
            if (!Schema::hasColumn('leads', 'business_address')) {
                $table->text('business_address')->nullable()->after('business_postal_code');
            }

            // DND settings as JSON
            if (!Schema::hasColumn('leads', 'dnd_settings')) {
                $table->json('dnd_settings')->nullable()->after('business_address');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'first_name',
                'last_name',
                'contact_type',
                'tags',
                'postal_code',
                'city',
                'state',
                'country',
                'business_name',
                'business_gst',
                'business_state',
                'business_postal_code',
                'business_address',
                'dnd_settings'
            ]);
        });
    }
};
