<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Pipeline extends Model
{
    protected $fillable = [
        'name',
        'created_by',
        'is_deleted',
    ];

    public function stages()
    {
        $query = $this->hasMany('App\Models\Stage', 'pipeline_id', 'id');

        // Only apply user filter if user is authenticated
        if (\Auth::check() && \Auth::user()) {
            $query->where('created_by', '=', \Auth::user()->ownerId());
        }

        return $query->orderBy('order');
    }

    public function leadStages()
    {
        $query = $this->hasMany('App\Models\LeadStage', 'pipeline_id', 'id');

        // Only apply user filter if user is authenticated
        if (\Auth::check() && \Auth::user()) {
            $query->where('created_by', '=', \Auth::user()->ownerId());
        }

        return $query->orderBy('order');
    }

    // Alternative methods for when we need to specify the user explicitly
    public function stagesForUser($userId = null)
    {
        $query = $this->hasMany('App\Models\Stage', 'pipeline_id', 'id');

        if ($userId) {
            $query->where('created_by', '=', $userId);
        }

        return $query->orderBy('order');
    }

    public function leadStagesForUser($userId = null)
    {
        $query = $this->hasMany('App\Models\LeadStage', 'pipeline_id', 'id');

        if ($userId) {
            $query->where('created_by', '=', $userId);
        }

        return $query->orderBy('order');
    }
}
