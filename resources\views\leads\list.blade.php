@extends('layouts.admin')
@section('page-title')
    {{__('Manage Leads')}} @if($pipeline) - {{$pipeline->name}} @endif
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{asset('css/summernote/summernote-bs4.css')}}">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .communication-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .communication-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .communication-btn.call { background-color: #28a745; }
        .communication-btn.sms { background-color: #17a2b8; }
        .communication-btn.email { background-color: #6f42c1; }
        .communication-btn.source { background-color: #fd7e14; }

        .communication-btn i {
            font-size: 14px;
        }

        .modal-body .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .communication-option-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .communication-option-item i {
            font-size: 20px;
            margin-right: 15px;
            width: 25px;
            text-align: center;
        }

        .communication-option-item span {
            font-size: 16px;
            font-weight: 500;
        }

        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        .data-source-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }
        .source-internal {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .lead-checkbox {
            display: none;
            margin-right: 10px;
        }
        tr.lead-row:hover .lead-checkbox {
            display: inline-block;
        }
        tr.show-checkboxes .lead-checkbox {
            display: inline-block !important;
        }
        #bulk-delete-btn {
            display: none;
            margin-bottom: 10px;
        }
        #select-all-btn {
            display: none;
        }
        /* Oval Pipeline Select Styling */
        #default_pipeline_id {
            border-radius: 25px !important;
            padding: 8px 20px 8px 20px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%236c63ff'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e") !important;
            background-position: right 12px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 12px !important;
            padding-right: 35px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }
        
        #default_pipeline_id:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        #default_pipeline_id:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: #ffffff !important;
        }
        
        #default_pipeline_id option {
            background: white !important;
            color: #374151 !important;
            padding: 8px 12px !important;
            border-radius: 8px !important;
        }
        
        /* Custom form wrapper styling for oval design */
        #change-pipeline {
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
            margin-bottom: 0px;
        }
        
        /* Ensure proper alignment with other action buttons */
        .float-end {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }
        
        /* Search Bar Styling - Same oval shape as pipeline select */
        .search-container {
            position: relative !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
        }
        
        .search-input {
            border-radius: 25px !important;
            padding: 6px 14px 6px 14px !important;
            padding-right: 45px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 200px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            transition: all 0.3s ease !important;
        }
        
        .search-input:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
        }
        
        .search-input:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        .search-input::placeholder {
            color: #9ca3af !important;
            font-weight: 400 !important;
        }
        
        .search-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
        }
        
        .clear-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
            transition: color 0.3s ease !important;
        }
        
        .clear-icon:hover {
            color: #dc3545 !important;
        }
        /* Select2 custom styling for oval pipeline select */
        .select2-container--default .select2-selection--single {
            border-radius: 25px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            padding: 8px 20px 8px 20px !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 12px !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 40px !important;
        }
    </style>
@endpush

@push('script-page')
    <!-- jQuery (if not already loaded) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{asset('css/summernote/summernote-bs4.js')}}"></script>
    <script>
        $(document).on('change', '#default_pipeline_id', function() {
            $('#change-pipeline').submit();
        });

        function openCommunicationModal(type) {
            const modal = new bootstrap.Modal(document.getElementById('communication-modal'));
            const title = document.getElementById('communication-modal-label');
            title.textContent = type === 'sms' ? 'SMS Options' : 'Email Options';
            modal.show();
        }

        function openWhatsApp(phoneNumber) {
            // Remove any non-numeric characters from phone number
            const cleanPhone = phoneNumber.replace(/\D/g, '');
            
            // Add country code if not present (assuming +1 for US, adjust as needed)
            let formattedPhone = cleanPhone;
            if (!cleanPhone.startsWith('1') && cleanPhone.length === 10) {
                formattedPhone = '1' + cleanPhone;
            }
            
            // Create WhatsApp URL
            const whatsappUrl = `https://wa.me/${formattedPhone}`;
            
            // Open WhatsApp in new tab/window
            window.open(whatsappUrl, '_blank');
        }

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '{{ __('No sources found for this lead.') }}';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }












        // Global function to handle filter form submission using jQuery hide/show
        window.handleFilterSubmit = function(form) {
            var nameFilter = $(form).find('input[name="name"]').val().toLowerCase();
            var emailFilter = $(form).find('input[name="email"]').val().toLowerCase();
            var stageFilter = $(form).find('select[name="stage_id"]').val();

            // Check if any filters are applied
            var hasFilters = nameFilter || emailFilter || stageFilter;

            // Show/hide filter active badge
            if (hasFilters) {
                $('#filter-active-badge').removeClass('d-none');
            } else {
                $('#filter-active-badge').addClass('d-none');
            }

            var visibleRowsCount = 0;

            // Filter table rows
            $('#leads-tbody tr').each(function() {
                var row = $(this);
                var leadName = row.find('.lead-name a').text().toLowerCase();
                var leadEmail = row.find('.lead-email').text().toLowerCase();
                var leadStage = row.find('td:nth-child(5)').text().toLowerCase(); // Stage column

                var showRow = true;

                // Apply name filter
                if (nameFilter && leadName.indexOf(nameFilter) === -1) {
                    showRow = false;
                }

                // Apply email filter
                if (emailFilter && leadEmail.indexOf(emailFilter) === -1) {
                    showRow = false;
                }

                // Apply stage filter
                if (stageFilter) {
                    // Get the stage name from the stages dropdown to match
                    var selectedStageName = $('#lead-filter-form select[name="stage_id"] option:selected').text().toLowerCase();
                    if (selectedStageName && leadStage.indexOf(selectedStageName) === -1) {
                        showRow = false;
                    }
                }

                if (showRow) {
                    row.show();
                    visibleRowsCount++;
                } else {
                    row.hide();
                }
            });

            // Show message if no results found
            if (visibleRowsCount === 0 && hasFilters) {
                if ($('#no-results-row').length === 0) {
                    $('#leads-tbody').append('<tr id="no-results-row"><td colspan="9" class="text-center py-4">{{__("No leads found matching the filter criteria.")}}</td></tr>');
                }
            } else {
                $('#no-results-row').remove();
            }

            // Close the off-canvas
            var offcanvasElement = document.getElementById('filterOffcanvas');
            var offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
                offcanvas.hide();
            }

            // Show success message
            show_toastr('Success', 'Filters applied successfully', 'success');

            return false; // Prevent default form submission
        };

        // Global function to clear filters
        window.clearFilters = function() {
            // Reset the filter form
            $('#lead-filter-form')[0].reset();

            // Reset select dropdown
            $('#lead-filter-form select').val('').trigger('change');

            // Hide filter active badge
            $('#filter-active-badge').addClass('d-none');

            // Show all table rows
            $('#leads-tbody tr').show();

            // Remove no results message
            $('#no-results-row').remove();

            // Show success message
            show_toastr('Success', 'Filters cleared successfully', 'success');
        };

        $(document).ready(function() {
            // Initialize Select2 after DOM is ready
            if (typeof $.fn.select2 !== 'undefined') {
                $('.select').select2({
                    placeholder: '{{__("Select Stage")}}',
                    allowClear: true
                });
            }

            // Handle off-canvas shown event
            $('#filterOffcanvas').on('shown.bs.offcanvas', function () {
                // Focus on first input when off-canvas is shown
                $(this).find('input[name="name"]').focus();
            });

            // Initialize Select2 for off-canvas filter
            if (typeof $.fn.select2 !== 'undefined') {
                $('#filterOffcanvas .select').select2({
                    dropdownParent: $('#filterOffcanvas'),
                    placeholder: '{{__("Select Stage")}}',
                    allowClear: true
                });
            }

            // ===== BULK ACTION LOGIC =====
            var bulkDeleteInProgress = false;
            
            // Show checkboxes on row hover
            $('#leads-tbody').on('mouseenter', 'tr.lead-row', function() {
                if (!$('tr.lead-row').hasClass('show-checkboxes')) {
                    $(this).find('.lead-checkbox').show();
                }
            });
            
            $('#leads-tbody').on('mouseleave', 'tr.lead-row', function() {
                if (!$('tr.lead-row').hasClass('show-checkboxes')) {
                    $(this).find('.lead-checkbox').hide();
                }
            });
            
            // Show all checkboxes when any checkbox is clicked
            $('#leads-tbody').on('click', '.lead-checkbox', function(e) {
                $('tr.lead-row').addClass('show-checkboxes');
                $('.lead-checkbox').show();
                updateButtons();
                e.stopPropagation();
            });
            
            // Update buttons when checkbox state changes
            $('#leads-tbody').on('change', '.lead-checkbox', function() {
                updateButtons();
            });
            
            // Select All button - FINAL FIX
            $(document).on('click', '#select-all-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();

                $('.lead-checkbox').show();
                $('tr.lead-row').addClass('show-checkboxes');
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');

                if (checkedVisibleCheckboxes.length === visibleCheckboxes.length && visibleCheckboxes.length > 0) {
                    // All are checked, so uncheck all
                    visibleCheckboxes.prop('checked', false).trigger('change');
                    $(this).text('{{__("Select All")}}');
                } else {
                    // Not all are checked, so check all
                    visibleCheckboxes.prop('checked', true).trigger('change');
                    $(this).text('{{__("Deselect All")}}');
                }
                updateButtons();
            });
            
            // Simple button update function
            function updateButtons() {
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedCheckboxes = $('.lead-checkbox:checked');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');
                
                console.log('Update buttons - Visible:', visibleCheckboxes.length, 'Checked:', checkedCheckboxes.length);
                
                // Show Select All button if any checkboxes are visible
                if (visibleCheckboxes.length > 0) {
                    $('#select-all-btn').show();
                    
                    // Update Select All button text
                    if (checkedVisibleCheckboxes.length === visibleCheckboxes.length && visibleCheckboxes.length > 0) {
                        $('#select-all-btn').text('{{__("Deselect All")}}');
                    } else {
                        $('#select-all-btn').text('{{__("Select All")}}');
                    }
                } else {
                    $('#select-all-btn').hide();
                }
                
                // Show Bulk Delete button if any checkboxes are checked
                if (checkedCheckboxes.length > 0) {
                    $('#bulk-delete-btn').show();
                    $('#bulk-delete-btn').text('{{__("Bulk Delete")}} (' + checkedCheckboxes.length + ')');
                } else {
                    $('#bulk-delete-btn').hide();
                }
            }
            
            // Hide everything when clicking outside (only if no checkboxes are visible)
            $(document).on('click', function(e) {
                // Only hide if clicking outside and NO checkboxes are visible
                if (
                    !$(e.target).closest('.lead-checkbox, #select-all-btn, #bulk-delete-btn').length &&
                    $('.lead-checkbox:visible').length === 0
                ) {
                    $('tr.lead-row').removeClass('show-checkboxes');
                    $('.lead-checkbox').hide();
                    $('#select-all-btn').hide();
                    $('#bulk-delete-btn').hide();
                }
            });
            
            // Bulk delete button click
            $('#bulk-delete-btn').on('click', function() {
                if ($('.lead-checkbox:checked').length === 0) return;
                
                var modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
                $('#confirmBulkDeleteBtn').prop('disabled', false);
                modal.show();
            });
            
            // Confirm bulk delete
            $('#confirmBulkDeleteBtn').on('click', function() {
                if (bulkDeleteInProgress) return;
                
                bulkDeleteInProgress = true;
                $(this).prop('disabled', true);
                
                var selected = $('.lead-checkbox:checked').map(function(){ 
                    return $(this).val(); 
                }).get();
                
                $.ajax({
                    url: '{{ route('leads.bulkDelete') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        lead_ids: selected
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        bulkDeleteInProgress = false;
                        $('#confirmBulkDeleteBtn').prop('disabled', false);
                        console.error('Bulk delete failed:', xhr);
                        alert('Bulk delete failed. Please try again.');
                    }
                });
            });

            // Debug button to log all checkboxes
            $('#debug-select-all-btn').on('click', function() {
                var allCheckboxes = $('.lead-checkbox');
                var checkedCheckboxes = $('.lead-checkbox:checked');
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');
                console.log('ALL checkboxes:', allCheckboxes.length, allCheckboxes);
                console.log('Checked checkboxes:', checkedCheckboxes.length, checkedCheckboxes);
                console.log('Visible checkboxes:', visibleCheckboxes.length, visibleCheckboxes);
                console.log('Checked visible checkboxes:', checkedVisibleCheckboxes.length, checkedVisibleCheckboxes);
            });

            $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });
        
        // Search functionality for leads
        $('#lead-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            // Show/hide clear icon based on input value
            if (searchTerm.length > 0) {
                $('.search-icon').hide();
                $('.clear-icon').show();
            } else {
                $('.search-icon').show();
                $('.clear-icon').hide();
            }
            
            var visibleRowsCount = 0;

            // Search through all table rows
            $('#leads-tbody tr').each(function() {
                var row = $(this);
                var leadName = row.find('.lead-name a').text().toLowerCase();
                var leadEmail = row.find('.lead-email').text().toLowerCase();
                var leadPhone = row.find('td:nth-child(8)').text().toLowerCase(); // Adjust if phone is in a different column

                // Check if search term matches any lead data
                var isMatch = leadName.includes(searchTerm) ||
                              leadEmail.includes(searchTerm) ||
                              leadPhone.includes(searchTerm);
                
                if (searchTerm === '' || isMatch) {
                    row.show();
                    visibleRowsCount++;
                } else {
                    row.hide();
                }
            });

            // Show message if no results found
            if (visibleRowsCount === 0 && searchTerm.length > 0) {
                if ($('#no-results-row').length === 0) {
                    $('#leads-tbody').append('<tr id="no-results-row"><td colspan="9" class="text-center py-4">{{__("No leads found matching the search criteria.")}}</td></tr>');
                }
            } else {
                $('#no-results-row').remove();
            }
        });
        
            // Search icon click handler
            $('.search-icon').on('click', function() {
                $('#lead-search').focus();
            });
        
            // Clear icon click handler
            $('.clear-icon').on('click', function() {
                $('#lead-search').val('').trigger('input');
                $('#lead-search').focus();
            });
        });
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Lead')}}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        <!-- Search Bar -->
        <div class="search-container me-2">
            <input type="text" 
                   id="lead-search" 
                   class="form-control search-input" 
                   placeholder="{{ __('Search leads...') }}" 
                   autocomplete="off">
            <i class="fas fa-search search-icon"></i>
            <i class="fas fa-times clear-icon" style="display: none;"></i>
        </div>
        
        {{ Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'btn btn-sm']) }}
        {{ Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['id' => 'default_pipeline_id', 'class' => 'me-2']) }}
        {{ Form::close() }}
        <a href="{{ route('leads.index') }}" data-size="lg"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="{{ __('Kanban View') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-layout-grid"></i>
        </a>
        <a href="#" data-size="md"
            data-bs-toggle="tooltip"
            data-ajax-popup="true"
            data-bs-placement="bottom"
            title="{{ __('Import') }}"
            data-url="{{ route('leads.import') }}"
            data-ajax-popup="true"
            data-title="{{ __('Import Lead CSV file') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-file-import"></i>
        </a>
        <a href="#"
            data-bs-toggle="offcanvas"
            data-bs-target="#filterOffcanvasKanban"
            aria-controls="filterOffcanvasKanban"
            data-bs-placement="bottom"
            title="{{ __('Filter Leads') }}"
            id="filter-btn-kanban"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px; border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white; box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-filter" style="font-size:16px;"></i>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none" id="filter-active-badge-kanban"></span>
        </a>
        <a href="{{ route('leads.export') }}"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="{{ __('Export') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-file-export"></i>
        </a>
        <a href="#"
            data-size="lg"
            data-url="{{ route('leads.create') }}"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="{{ __('Create New Lead') }}"
            data-title="{{ __('Create Lead') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;""
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    @if($pipeline)
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">{{__('Leads List')}}</h5>
                            </div>
                            <div class="col-auto">
                                <button id="select-all-btn" class="btn btn-info btn-sm me-2" style="display: none;">{{__('Select All')}}</button>
                                <button id="bulk-delete-btn" class="btn btn-danger btn-sm" style="display: none;">{{__('Bulk Delete')}}</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable" id="leads-table">
                                <thead>
                                <tr>
                                    <th></th> <!-- Checkbox column -->
                                    <th>{{__('Source')}}</th>
                                    <th>{{__('Name')}}</th>
                                    <th>{{__('Email')}}</th>
                                    <!-- <th>{{__('Subject')}}</th> -->
                                    <th>{{__('Stage')}}</th>
                                    <th>{{__('Assigned To')}}</th>
                                    <th>{{__('Follow-Up Date')}}</th>
                                    <th>{{__('Contact')}}</th>
                                    <th>{{__('Action')}}</th>
                                </tr>
                                </thead>
                                <tbody id="leads-tbody">
                                @if(count($leads) > 0)
                                    @foreach ($leads as $lead)
                                        @php
                                            $today = \Carbon\Carbon::today();
                                            $followUp = $lead->next_follow_up_date ? \Carbon\Carbon::parse($lead->next_follow_up_date) : null;
                                            $highlight = '';
                                            if ($followUp) {
                                                if ($followUp->isToday()) {
                                                    $highlight = 'badge bg-warning text-dark';
                                                } elseif ($followUp->isPast()) {
                                                    $highlight = 'badge bg-danger';
                                                }
                                            }
                                        @endphp
                                        <tr class="internal-lead lead-row" data-source="internal">
                                            <td>
                                                <input type="checkbox" class="lead-checkbox" value="{{$lead->id}}" />
                                            </td>
                                            <td>
                                                <span class="data-source-badge source-internal">
                                                    <i class="ti ti-database"></i> {{__('Internal')}}
                                                </span>
                                            </td>
                                            <td class="lead-name">
                                                <a href="{{ route('leads.show', $lead->id) }}" class="text-primary text-decoration-underline" style="cursor:pointer;">
                                                    {{ $lead->name }}
                                                </a>
                                            </td>
                                            <td class="lead-email">{{ $lead->email }}</td>
                                            <!-- <td>{{ $lead->subject }}</td> -->
                                            <td>{{  !empty($lead->stage)?$lead->stage->name:'-' }}</td>
                                            <td>
                                                @foreach($lead->users as $user)
                                                <div class="d-flex align-items-center">
                                                        <a href="#"
                                                        class="btn btn-sm me-1 p-0 d-flex align-items-center justify-content-center rounded-circle"
                                                        style="width: 30px; height: 30px; background-color: #f0f0f0; transition: all 0.3s ease;"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="{{ $user->name }}">
                                                            <i class="ti ti-user" style="font-size: 16px; color: #333;"></i>
                                                        </a>
                                                    @endforeach
                                                </div>
                                            </td>
                                            <td>
                                                @if($lead->next_follow_up_date)
                                                    <span class="{{ $highlight }}">
                                                        {{ \Carbon\Carbon::parse($lead->next_follow_up_date)->format('d-m-Y') }}
                                                    </span>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                <?php
                                                $sources = $lead->sources();
                                                ?>
                                                <div class="communication-buttons">
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                        style="
                                                            background: linear-gradient(135deg, #6f42c1, #065f46);
                                                            border: none;
                                                            border-radius: 8px;
                                                            padding: 8px 12px;
                                                            transition: all 0.3s ease;
                                                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                        "
                                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                        data-bs-toggle="tooltip"
                                                        title="Call"
                                                        onclick="window.open('tel:{{ $lead->phone }}', '_self')">
                                                        <i class="fas fa-phone-alt text-white" style="font-size: 12px;"></i>
                                                    </button>

                                                    {{-- Whatsapp Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #1ebea5, #00e676);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="WhatsApp"
                                                            onclick="openWhatsApp('{{ $lead->phone }}')">
                                                        <i class="fab fa-whatsapp text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Email Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #0d6efd, #0b5ed7);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="Email"
                                                            onclick="openCommunicationModal('email')">
                                                        <i class="fas fa-envelope text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Sources Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                border: 1px solid #0d6efd;
                                                                background-color: transparent;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                            "
                                                            onmouseover="this.style.backgroundColor='#0d6efd'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#0d6efd'; this.style.boxShadow='none'"
                                                            title="Activity"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#activityModal-{{ $lead->id }}">
                                                        <i class="fas fa-stream" style="font-size: 16px; color: #0d6efd;"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            @if(Auth::user()->type != 'client')
                                                <td class="Action">
                                                    <span>
                                                    @can('view lead')
                                                            @if($lead->is_active)
                                                            <!-- <div class="action-btn">
                                                                <a href="{{ route('leads.show', $lead->id) }}"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                        background: linear-gradient(135deg, #14532d, #065f46); 
                                                                        border: none;
                                                                        border-radius: 8px;
                                                                        padding: 8px 12px;
                                                                        transition: all 0.3s ease;
                                                                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                                "
                                                                onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                                data-size="xl"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('View') }}"
                                                                data-title="{{ __('Lead Detail') }}">
                                                                    <i class="ti ti-eye text-white" style="font-size: 16px;"></i>
                                                                </a>
                                                            </div> -->
                                                            @endif
                                                        @endcan
                                                        @can('edit lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            <a href="#"
                                                            class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #14532d, #065f46); 
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-url="{{ route('leads.edit', $lead->id) }}"
                                                            data-ajax-popup="true"
                                                            data-size="xl"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ __('Edit') }}"
                                                            data-title="{{ __('Lead Edit') }}">
                                                                <i class="ti ti-pencil text-white" style="font-size: 16px;"></i>
                                                            </a>
                                                        </div>
                                                        @endcan
                                                        @can('delete lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['leads.destroy', $lead->id], 'id' => 'delete-form-' . $lead->id]) !!}
                                                                <a href="#"
                                                                onclick="event.preventDefault(); document.getElementById('delete-form-{{ $lead->id }}').submit();"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                    border: 1px solid #dc3545;
                                                                    background-color: transparent;
                                                                    border-radius: 8px;
                                                                    padding: 8px 12px;
                                                                    transition: all 0.3s ease;
                                                                "
                                                                onmouseover="this.style.backgroundColor='#dc3545'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#14532d'; this.style.boxShadow='none'"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}">
                                                                    <i class="ti ti-trash" style="font-size: 16px; color: #14532d;"></i>
                                                                </a>
                                                            {!! Form::close() !!}
                                                        </div>
                                                        @endcan
                                                    </span>
                                                </td>
                                            @endif
                                        </tr>
                                        <!-- Activity Modal for this lead -->
                                        <div class="modal fade" id="activityModal-{{ $lead->id }}" tabindex="-1" aria-labelledby="activityModalLabel-{{ $lead->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="activityModalLabel-{{ $lead->id }}">{{ __('Activity') }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row leads-scroll">
                                                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                                                @if (!$lead->activities->isEmpty())
                                                                    @foreach ($lead->activities as $activity)
                                                                        <li class="list-group-item card mb-3">
                                                                            <div class="row align-items-center justify-content-between">
                                                                                <div class="col-auto mb-3 mb-sm-0">
                                                                                    <div class="d-flex align-items-center">
                                                                                        <div class="theme-avtar bg-primary badge">
                                                                                            <i class="ti {{ $activity->logIcon() }}"></i>
                                                                                        </div>
                                                                                        <div class="ms-3">
                                                                                            <span class="text-dark text-sm">{{ __($activity->log_type) }}</span>
                                                                                            <h6 class="m-0">{!! $activity->getLeadRemark() !!}</h6>
                                                                                            <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </li>
                                                                    @endforeach
                                                                @else
                                                                    <li class="text-center py-4">No activity found yet.</li>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <tr class="font-style no-data-row">
                                        <td colspan="7" class="text-center">{{ __('No data available in table') }}</td>
                                    </tr>
                                @endif

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Communication Modal -->
    <div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="communication-modal-label">Communication Options</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="communication-options-list">
                        <a href="#" class="communication-option-item" id="whatsapp-option">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                        <a href="#" class="communication-option-item" id="default-email-option">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>Default Email App</span>
                        </a>
                        <a href="#" class="communication-option-item" id="cloud-email-option">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Cloud Email Service</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sources Modal -->
    <div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sources-modal-label">{{ __('Lead Sources') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="list-group" id="sources-list">
                        <!-- Sources will be dynamically inserted here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkDeleteModalLabel">{{ __('Confirm Bulk Delete') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('Are you sure you want to delete the selected leads? This action cannot be undone.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('No') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn">{{ __('Yes') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Off-canvas for List View -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas" aria-labelledby="filterOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="filterOffcanvasLabel">{{__('Filter Leads')}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            {{ Form::open(['id' => 'lead-filter-form', 'onsubmit' => 'return handleFilterSubmit(this)']) }}

            <div class="mb-3">
                {{ Form::label('name', __('Name'), ['class' => 'form-label']) }}
                {{ Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter lead name')]) }}
            </div>

            <div class="mb-3">
                {{ Form::label('email', __('Email'), ['class' => 'form-label']) }}
                {{ Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter email address')]) }}
            </div>

            <div class="mb-3">
                {{ Form::label('stage_id', __('Stage'), ['class' => 'form-label']) }}
                {{ Form::select('stage_id', $stages, null, ['class' => 'form-control select', 'placeholder' => __('Select Stage')]) }}
            </div>

            <div class="d-grid gap-2">
                <input type="submit" value="{{ __('Apply Filter') }}" class="btn btn-primary">
                <input type="button" value="{{ __('Clear Filters') }}" class="btn btn-secondary" onclick="clearFilters()">
            </div>

            {{ Form::close() }}
        </div>
    </div>

@endsection