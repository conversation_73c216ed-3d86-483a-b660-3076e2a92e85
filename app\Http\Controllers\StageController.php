<?php

namespace App\Http\Controllers;

use App\Models\Deal;
use App\Models\Pipeline;
use App\Models\Stage;
use Illuminate\Http\Request;

class StageController extends Controller
{
    public function __construct()
    {
        $this->middleware(
            [
                'auth',
                'XSS',
            ]
        );
    }

    /**
     * Display a listing of the restage.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(\Auth::user()->can('manage stage'))
        {
            $stages    = Stage::select('stages.*', 'pipelines.name as pipeline')->join('pipelines', 'pipelines.id', '=', 'stages.pipeline_id')->where('pipelines.created_by', '=', \Auth::user()->ownerId())->where('stages.created_by', '=', \Auth::user()->ownerId())->orderBy('stages.pipeline_id')->orderBy('stages.order')->get();
            $pipelines = [];

            foreach($stages as $stage)
            {
                if(!array_key_exists($stage->pipeline_id, $pipelines))
                {
                    $pipelines[$stage->pipeline_id]           = [];
                    $pipelines[$stage->pipeline_id]['name']   = $stage['pipeline'];
                    $pipelines[$stage->pipeline_id]['stages'] = [];
                }
                $pipelines[$stage->pipeline_id]['stages'][] = $stage;
            }

            return view('stages.index')->with('pipelines', $pipelines);
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new restage.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if(\Auth::user()->can('create stage'))
        {
            $pipelines = Pipeline::where('created_by', '=', \Auth::user()->ownerId())->get()->pluck('name', 'id');

            return view('stages.create')->with('pipelines', $pipelines);
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Store a newly created restage in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if(\Auth::user()->can('create stage'))
        {
            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required|max:20',
                                   'pipeline_id' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                // Check if request is AJAX or from pipeline page
                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json(['error' => $messages->first()], 422);
                }

                // Check if request came from pipeline page
                $referer = $request->headers->get('referer');
                if ($referer && str_contains($referer, '/pipeline')) {
                    return response()->json(['error' => $messages->first()], 422);
                }

                return redirect()->route('stages.index')->with('error', $messages->first());
            }

            try {
                \DB::beginTransaction();

                // Get the next order for lead stage
                $leadStageOrder = \App\Models\LeadStage::where('pipeline_id', $request->pipeline_id)
                    ->where('created_by', \Auth::user()->ownerId())
                    ->max('order') + 1;

                // Create Lead Stage only
                $leadStage              = new \App\Models\LeadStage();
                $leadStage->name        = $request->name;
                $leadStage->pipeline_id = $request->pipeline_id;
                $leadStage->created_by  = \Auth::user()->ownerId();
                $leadStage->order       = $leadStageOrder;
                $leadStage->save();

                \DB::commit();

                $message = __('Stage created successfully!');

                // Check if request is AJAX or from pipeline page
                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => $message,
                        'data' => [
                            'lead_stage' => $leadStage
                        ]
                    ]);
                }

                // Check if request came from pipeline page
                $referer = $request->headers->get('referer');
                if ($referer && str_contains($referer, '/pipeline')) {
                    return response()->json([
                        'success' => true,
                        'message' => $message,
                        'data' => [
                            'lead_stage' => $leadStage
                        ]
                    ]);
                }

                return redirect()->route('stages.index')->with('success', $message);

            } catch (\Exception $e) {
                \DB::rollback();

                $errorMessage = __('An error occurred while creating the stage.');

                // Check if request is AJAX or from pipeline page
                if ($request->expectsJson() || $request->is('api/*') || $request->ajax()) {
                    return response()->json(['error' => $errorMessage], 500);
                }

                // Check if request came from pipeline page
                $referer = $request->headers->get('referer');
                if ($referer && str_contains($referer, '/pipeline')) {
                    return response()->json(['error' => $errorMessage], 500);
                }

                return redirect()->route('stages.index')->with('error', $errorMessage);
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified restage.
     *
     * @param \App\Stage $stage
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Stage $stage)
    {
        return redirect()->route('stages.index');
    }

    /**
     * Show the form for editing the specified restage.
     *
     * @param \App\Stage $stage
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Stage $stage)
    {
        if(\Auth::user()->can('edit stage'))
        {
            if($stage->created_by == \Auth::user()->ownerId())
            {
                $pipelines = Pipeline::where('created_by', '=', \Auth::user()->ownerId())->get()->pluck('name', 'id');

                return view('stages.edit', compact('stage', 'pipelines'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Update the specified restage in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Stage $stage
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Stage $stage)
    {
        if(\Auth::user()->can('edit stage'))
        {

            if($stage->created_by == \Auth::user()->ownerId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required|max:20',
                                       'pipeline_id' => 'required',
                                   ]
                );
                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();
                    return redirect()->route('stages.index')->with('error', $messages->first());
                }

                try {
                    \DB::beginTransaction();

                    // Update Deal Stage
                    $stage->name        = $request->name;
                    $stage->pipeline_id = $request->pipeline_id;
                    $stage->save();

                    // Find and update corresponding Lead Stage
                    $leadStage = \App\Models\LeadStage::where('name', $stage->getOriginal('name'))
                        ->where('pipeline_id', $stage->getOriginal('pipeline_id'))
                        ->where('created_by', \Auth::user()->ownerId())
                        ->first();

                    if ($leadStage) {
                        $leadStage->name        = $request->name;
                        $leadStage->pipeline_id = $request->pipeline_id;
                        $leadStage->save();
                    }

                    \DB::commit();

                    $message = __('Stage successfully updated!');

                    if ($request->expectsJson() || $request->is('api/*')) {
                        return response()->json([
                            'success' => true,
                            'message' => $message,
                            'data' => [
                                'deal_stage' => $stage,
                                'lead_stage' => $leadStage
                            ]
                        ]);
                    }

                    return redirect()->route('stages.index')->with('success', $message);

                } catch (\Exception $e) {
                    \DB::rollback();

                    $errorMessage = __('An error occurred while updating the stage.');

                    if ($request->expectsJson() || $request->is('api/*')) {
                        return response()->json(['error' => $errorMessage], 500);
                    }

                    return redirect()->route('stages.index')->with('error', $errorMessage);
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified restage from storage.
     *
     * @param \App\Stage $stage
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Stage $stage)
    {
        if(\Auth::user()->can('delete stage'))
        {
            if($stage->created_by == \Auth::user()->ownerId())
            {
                $deals = Deal::where('stage_id', '=', $stage->id)->where('created_by', '=', $stage->created_by)->count();

                // Check for leads in corresponding lead stage
                $leadStage = \App\Models\LeadStage::where('name', $stage->name)
                    ->where('pipeline_id', $stage->pipeline_id)
                    ->where('created_by', \Auth::user()->ownerId())
                    ->first();

                $leads = 0;
                if ($leadStage) {
                    $leads = \App\Models\Lead::where('stage_id', '=', $leadStage->id)
                        ->where('created_by', '=', $stage->created_by)
                        ->count();
                }

                if($deals == 0 && $leads == 0)
                {
                    try {
                        \DB::beginTransaction();

                        // Delete the deal stage
                        $stage->delete();

                        // Delete the corresponding lead stage if it exists
                        if ($leadStage) {
                            $leadStage->delete();
                        }

                        \DB::commit();

                        return redirect()->route('stages.index')->with('success', __('Stage successfully deleted!'));

                    } catch (\Exception $e) {
                        \DB::rollback();
                        return redirect()->route('stages.index')->with('error', __('An error occurred while deleting the stage.'));
                    }
                }
                else
                {
                    $errorMessage = '';
                    if ($deals > 0 && $leads > 0) {
                        $errorMessage = __('There are deals and leads in this stage, please remove them first!');
                    } elseif ($deals > 0) {
                        $errorMessage = __('There are deals in this stage, please remove them first!');
                    } elseif ($leads > 0) {
                        $errorMessage = __('There are leads in this stage, please remove them first!');
                    }

                    return redirect()->route('stages.index')->with('error', $errorMessage);
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function order(Request $request)
    {
        $post = $request->all();
        foreach($post['order'] as $key => $item)
        {
            $stage        = Stage::where('id', '=', $item)->first();
            $stage->order = $key;
            $stage->save();
        }
    }

    public function json(Request $request)
    {
        $stage = new Stage();
        if($request->pipeline_id)
        {
            $stage = $stage->where('pipeline_id', '=', $request->pipeline_id);
            $stage = $stage->get()->pluck('name', 'id');
        }
        else
        {
            $stage = [];
        }


        return response()->json($stage);
    }
}
