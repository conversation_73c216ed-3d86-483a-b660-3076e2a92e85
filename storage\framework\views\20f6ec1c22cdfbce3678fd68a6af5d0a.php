

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Contact Groups')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('contacts.index')); ?>"><?php echo e(__('Contacts')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Contact Groups')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="<?php echo e(route('contacts.index')); ?>" class="btn btn-sm btn-primary">
            <i class="fa fa-arrow-left"></i> <?php echo e(__('Back to Contacts')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="contact-groups-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Contact Group Name')); ?></th>
                                    <th><?php echo e(__('Total Contact Numbers')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $contactGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        <i class="fas fa-users text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong><?php echo e($group->name); ?></strong>
                                                    <?php if($group->description): ?>
                                                        <br><small class="text-muted"><?php echo e($group->description); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?php echo e($group->leads_count); ?></span>
                                        </td>
                                        <td class="text-center">
                                            <div class="action-btn">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="viewGroupMembers('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>')"><i class="fa fa-eye me-2"></i><?php echo e(__('View Members')); ?></a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editContactGroup('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>', '<?php echo e($group->description); ?>')"><i class="fa fa-edit me-2"></i><?php echo e(__('Edit')); ?></a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteContactGroup('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>')"><i class="fa fa-trash me-2"></i><?php echo e(__('Delete')); ?></a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="3" class="text-center">
                                            <div class="py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted"><?php echo e(__('No Contact Groups Found')); ?></h5>
                                                <p class="text-muted"><?php echo e(__('Create contact groups from the contacts page by selecting contacts and clicking "Add to Contact Group"')); ?></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Group Members Modal -->
    <div class="modal fade" id="viewGroupMembersModal" tabindex="-1" aria-labelledby="viewGroupMembersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewGroupMembersModalLabel"><?php echo e(__('Group Members')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="group-members-content">
                        <!-- Members will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        function viewGroupMembers(groupId, groupName) {
            $('#viewGroupMembersModalLabel').text('Members of ' + groupName);
            $('#group-members-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading members...</div>');
            $('#viewGroupMembersModal').modal('show');
            
            // Load group members via AJAX
            $.ajax({
                url: '/contact-groups/' + groupId + '/members',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.members) {
                        let html = '<div class="table-responsive"><table class="table"><thead><tr><th>Name</th><th>Type</th><th>Email</th><th>Phone</th><th>Action</th></tr></thead><tbody>';

                        if (response.members.length > 0) {
                            response.members.forEach(function(member) {
                                html += '<tr>';
                                html += '<td>' + member.name + '</td>';
                                html += '<td><span class="badge bg-' + (member.type === 'Lead' ? 'success' : 'info') + '">' + member.type + '</span></td>';
                                html += '<td>' + (member.email || '-') + '</td>';
                                html += '<td>' + (member.phone || '-') + '</td>';
                                html += '<td><button class="btn btn-sm btn-outline-danger" onclick="removeMemberFromGroup(' + groupId + ', ' + member.id + ', \'' + member.name + '\')"><i class="fa fa-trash"></i></button></td>';
                                html += '</tr>';
                            });
                        } else {
                            html += '<tr><td colspan="5" class="text-center">No members found</td></tr>';
                        }

                        html += '</tbody></table></div>';
                        $('#group-members-content').html(html);
                    } else {
                        $('#group-members-content').html('<div class="alert alert-warning">Failed to load group members</div>');
                    }
                },
                error: function() {
                    $('#group-members-content').html('<div class="alert alert-danger">Error loading group members</div>');
                }
            });
        }

        function editContactGroup(groupId, groupName, groupDescription) {
            // Implementation for editing contact group
            console.log('Edit group:', groupId, groupName, groupDescription);
        }

        function removeMemberFromGroup(groupId, leadId, leadName) {
            if (confirm('Are you sure you want to remove "' + leadName + '" from this contact group?')) {
                $.ajax({
                    url: '/contact-groups/' + groupId + '/leads/' + leadId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            // Refresh the members list
                            viewGroupMembers(groupId, $('#viewGroupMembersModalLabel').text().replace('Members of ', ''));
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error removing member from group');
                    }
                });
            }
        }

        function deleteContactGroup(groupId, groupName) {
            if (confirm('Are you sure you want to delete the contact group "' + groupName + '"? This action cannot be undone.')) {
                $.ajax({
                    url: '/contact-groups/' + groupId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            location.reload();
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error deleting contact group');
                    }
                });
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/contact-groups/index.blade.php ENDPATH**/ ?>