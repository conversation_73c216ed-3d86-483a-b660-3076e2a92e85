@extends('layouts.admin')
@section('page-title')
    {{__('Manage Pipelines')}}
@endsection

@push('css-page')
    <style>
        .pipeline-stats {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .stat-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .stat-badge.lead-stages {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-badge.deal-stages {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-badge.leads-count {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-badge.deals-count {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .pipeline-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .pipeline-created {
            color: #718096;
            font-size: 0.875rem;
            margin-bottom: 10px;
        }
        .table td {
            vertical-align: middle;
            padding: 1rem 0.75rem;
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .btn-action {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
    </style>
@endpush

@push('script-page')
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            console.log('Pipeline index page loaded, jQuery version:', $.fn.jquery);
            
            // Clear any existing event handlers to prevent duplicates
            $(document).off('click.addCreateStageBtn');
            $(document).off('click.loadDefaultStagesBtn');
            $(document).off('click.removeCreateStageBtn');
            $(document).off('click.addStageBtn');
            $(document).off('click.removeStageBtn');

            // Global flags to prevent duplicate submissions
            window.stageSubmissionInProgress = false;
            window.pipelineDeleteInProgress = false;
            window.pipelineEditInProgress = false;
            window.pipelineCreateInProgress = false;

            // Reset flags when modals are opened
            $(document).on('click', 'a[data-url*="/stages/create"]', function(e) {
                // Let the default ajax-popup handle the modal opening
                // but we'll handle the form submission ourselves
                window.stageSubmissionInProgress = false; // Reset flag when opening modal
            });

            // Reset flags when delete modal is opened
            $('#deletePipelineModal').on('show.bs.modal', function (event) {
                window.pipelineDeleteInProgress = false;
            });

            // Reset flags when edit modal is opened
            $('#editPipelineModal').on('show.bs.modal', function (event) {
                window.pipelineEditInProgress = false;
            });

            // Reset flags when create modal is opened
            $('#createPipelineModal').on('show.bs.modal', function (event) {
                console.log('Create pipeline modal opening...');
                window.pipelineCreateInProgress = false;
                // Load default stages when modal opens
                setTimeout(function() {
                    loadDefaultCreateStages();
                }, 100); // Small delay to ensure modal is fully rendered
            });

            // Backup handler for create pipeline button click
            $(document).on('click', '#createPipelineBtn', function() {
                console.log('Create pipeline button clicked');
                setTimeout(function() {
                    if ($('#createStagesContainer').children().length === 0) {
                        console.log('No stages found, loading defaults...');
                        loadDefaultCreateStages();
                    }
                }, 200);
            });

            // Handle stage creation form submission - use specific form class
            $(document).off('submit', '.stage-create-form').on('submit', '.stage-create-form', function(e) {
                console.log('Stage form submission triggered');
                e.preventDefault();
                e.stopPropagation();

                var form = $(this);

                // Check if form is already being submitted (both local and global flags)
                if (form.data('submitting') || window.stageSubmissionInProgress) {
                    console.log('Form already submitting, preventing duplicate submission');
                    return false;
                }

                // Mark form as being submitted (both local and global flags)
                form.data('submitting', true);
                window.stageSubmissionInProgress = true;
                console.log('Starting stage creation AJAX request');

                var formData = form.serialize();
                var actionUrl = form.attr('action');

                $.ajax({
                    url: actionUrl,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Reset submitting flags
                        form.data('submitting', false);
                        window.stageSubmissionInProgress = false;

                        // Close the modal
                        $('.modal').modal('hide');

                        // Show success toast
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', response.message || 'Stage created successfully!');
                        } else {
                            alert(response.message || 'Stage created successfully!');
                        }

                        // Reset the form
                        form[0].reset();

                        // Stay on the same page - no reload needed
                        console.log('Stage created successfully:', response.data);
                    },
                    error: function(xhr) {
                        // Reset submitting flags
                        form.data('submitting', false);
                        window.stageSubmissionInProgress = false;

                        var errorMessage = 'An error occurred while creating the stage.';

                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show error toast
                        if (typeof show_toastr === 'function') {
                            show_toastr('error', errorMessage);
                        } else {
                            alert(errorMessage);
                        }
                    }
                });

                return false;
            });

            // Handle Edit Pipeline Modal
            $('#editPipelineModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var pipelineId = button.data('pipeline-id');
                var pipelineName = button.data('pipeline-name');

                var modal = $(this);
                modal.find('#edit_pipeline_name').val(pipelineName);
                modal.find('#editPipelineForm').attr('action', '{{ url("pipelines") }}/' + pipelineId);

                // Load stages for this pipeline
                loadPipelineStages(pipelineId);
            });

            // Function to load pipeline stages
            function loadPipelineStages(pipelineId) {
                $.ajax({
                    url: '{{ url("pipelines") }}/' + pipelineId + '/stages',
                    type: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        var stagesContainer = $('#stagesContainer');
                        stagesContainer.empty();

                        if (response.stages && response.stages.length > 0) {
                            response.stages.forEach(function(stage, index) {
                                addStageRow(stage, index);
                            });
                        } else {
                            stagesContainer.html('<p class="text-muted">{{__("No stages found for this pipeline.")}}</p>');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error loading stages:', xhr);
                        $('#stagesContainer').html('<p class="text-danger">{{__("Error loading stages.")}}</p>');
                    }
                });
            }

            // Function to add a stage row for edit modal
            function addStageRow(stage, index) {
                console.log('Adding edit stage row:', stage, index);
                var stageHtml = `
                    <div class="stage-row mb-3 p-3 border rounded" data-stage-id="${stage ? stage.id : ''}">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <label class="form-label">{{__('Stage Name')}}</label>
                                <input type="text" class="form-control stage-name" name="stages[${index}][name]"
                                       value="${stage ? stage.name : ''}" required maxlength="20"
                                       placeholder="{{__('Enter Stage Name')}}">
                                <input type="hidden" name="stages[${index}][id]" value="${stage ? stage.id : ''}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">{{__('Order')}}</label>
                                <input type="number" class="form-control stage-order" name="stages[${index}][order]"
                                       value="${stage ? stage.order : index + 1}" min="1">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-danger btn-sm d-block remove-stage-btn">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                $('#stagesContainer').append(stageHtml);
                console.log('Edit stage row added, total rows:', $('.stage-row').length);
            }

            // Add new stage button handler for edit modal
            $(document).on('click.addStageBtn', '#addStageBtn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Add edit stage button clicked');
                var currentStageCount = $('.stage-row').length;
                console.log('Current edit stage count:', currentStageCount);
                addStageRow({name: '', order: currentStageCount + 1}, currentStageCount);
            });

            // Remove stage button handler
            $(document).on('click.removeStageBtn', '.remove-stage-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).closest('.stage-row').remove();
                // Reindex the stages
                reindexStages();
            });

            // Function to reindex stages after removal
            function reindexStages() {
                $('.stage-row').each(function(index) {
                    $(this).find('input[name*="[name]"]').attr('name', `stages[${index}][name]`);
                    $(this).find('input[name*="[id]"]').attr('name', `stages[${index}][id]`);
                    $(this).find('input[name*="[order]"]').attr('name', `stages[${index}][order]`).val(index + 1);
                });
            }

            // Function to load default stages for create modal
            function loadDefaultCreateStages() {
                console.log('Loading default create stages...');
                var createStagesContainer = $('#createStagesContainer');
                createStagesContainer.empty();

                // Add default stages
                var defaultStages = ['New', 'Qualified', 'Discussion', 'Negotiation', 'Won/Lost'];
                console.log('Default stages to add:', defaultStages);

                // defaultStages.forEach(function(stageName, index) {
                //     console.log('Adding default stage:', stageName, 'at index:', index);
                //     addCreateStageRow({name: stageName, order: index + 1}, index);
                // });

                console.log('Default stages loaded, total rows:', $('.create-stage-row').length);
            }

            // Function to add a stage row for create modal
            function addCreateStageRow(stage, index) {
                console.log('Adding create stage row:', stage, index);
                
                // Prevent duplicate calls
                if (window.addingStageRow) {
                    console.log('Already adding stage row, ignoring duplicate call');
                    return;
                }
                
                window.addingStageRow = true;
                
                var stageHtml = `
                    <div class="create-stage-row mb-3 p-3 border rounded">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <label class="form-label">{{__('Stage Name')}}</label>
                                <input type="text" class="form-control create-stage-name" name="stages[${index}][name]"
                                       value="${stage ? stage.name : ''}" required maxlength="20"
                                       placeholder="{{__('Enter Stage Name')}}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">{{__('Order')}}</label>
                                <input type="number" class="form-control create-stage-order" name="stages[${index}][order]"
                                       value="${stage ? stage.order : index + 1}" min="1">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-danger btn-sm d-block remove-create-stage-btn">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                $('#createStagesContainer').append(stageHtml);
                console.log('Create stage row added, total rows:', $('.create-stage-row').length);
                
                // Reset flag after a short delay
                setTimeout(() => {
                    window.addingStageRow = false;
                }, 100);
            }

            // Load defaults button handler
            $(document).on('click.loadDefaultStagesBtn', '#loadDefaultStagesBtn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Load defaults button clicked');
                loadDefaultCreateStages();
            });

            // Add new stage button handler for create modal
            $(document).on('click.addCreateStageBtn', '#addCreateStageBtn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Add create stage button clicked - Event:', e);
                var currentStageCount = $('.create-stage-row').length;
                console.log('Current stage count:', currentStageCount);
                
                // Prevent multiple rapid clicks
                if ($(this).data('processing')) {
                    console.log('Button already processing, ignoring click');
                    return false;
                }
                
                $(this).data('processing', true);
                setTimeout(() => {
                    $(this).data('processing', false);
                }, 500);
                
                addCreateStageRow({name: '', order: currentStageCount + 1}, currentStageCount);
            });

            // Remove stage button handler for create modal
            $(document).on('click.removeCreateStageBtn', '.remove-create-stage-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).closest('.create-stage-row').remove();
                // Reindex the create stages
                reindexCreateStages();
            });

            // Function to reindex create stages after removal
            function reindexCreateStages() {
                $('.create-stage-row').each(function(index) {
                    $(this).find('input[name*="[name]"]').attr('name', `stages[${index}][name]`);
                    $(this).find('input[name*="[order]"]').attr('name', `stages[${index}][order]`).val(index + 1);
                });
            }

            // Handle Create Pipeline Form Submission
            $('#createPipelineForm').off('submit').on('submit', function(e) {
                console.log('Create pipeline form submission triggered');
                e.preventDefault();
                e.stopPropagation();

                var form = $(this);

                // Check if creation is already in progress
                if (form.data('creating') || window.pipelineCreateInProgress) {
                    console.log('Pipeline creation already in progress, preventing duplicate submission');
                    return false;
                }

                // Mark creation as in progress
                form.data('creating', true);
                window.pipelineCreateInProgress = true;
                console.log('Starting pipeline creation AJAX request');

                // Ensure all stage inputs have proper names and values
                $('.create-stage-row').each(function(index) {
                    $(this).find('input[name*="[name]"]').attr('name', `stages[${index}][name]`);
                    $(this).find('input[name*="[order]"]').attr('name', `stages[${index}][order]`).val(index + 1);
                });

                var formData = form.serialize();
                var actionUrl = form.attr('action');

                $.ajax({
                    url: actionUrl,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Reset creation flags
                        form.data('creating', false);
                        window.pipelineCreateInProgress = false;

                        $('#createPipelineModal').modal('hide');

                        // Show success message
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', response.message || '{{__("Pipeline and stages created successfully!")}}');
                        } else {
                            alert(response.message || '{{__("Pipeline and stages created successfully!")}}');
                        }

                        // Reset the form
                        form[0].reset();

                        // Reload the page to show updated data
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        // Reset creation flags
                        form.data('creating', false);
                        window.pipelineCreateInProgress = false;

                        var errorMessage = '{{__("An error occurred while creating the pipeline and stages.")}}';

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            var errors = xhr.responseJSON.errors;
                            errorMessage = Object.values(errors).flat().join(', ');
                        } else if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        if (typeof show_toastr === 'function') {
                            show_toastr('error', errorMessage);
                        } else {
                            alert(errorMessage);
                        }
                    }
                });

                return false;
            });

            // Handle Delete Pipeline Modal
            $('#deletePipelineModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var pipelineId = button.data('pipeline-id');
                var pipelineName = button.data('pipeline-name');

                var modal = $(this);
                modal.find('#delete_pipeline_name').text(pipelineName);
                modal.find('#deletePipelineForm').attr('action', '{{ url("pipelines") }}/' + pipelineId);

                // Reset checkbox and warning
                modal.find('#permanentDeleteCheckbox').prop('checked', false);
                modal.find('#permanentDeleteWarning').addClass('d-none');
            });

            // Handle checkbox change for permanent delete warning
            $('#permanentDeleteCheckbox').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#permanentDeleteWarning').removeClass('d-none');
                } else {
                    $('#permanentDeleteWarning').addClass('d-none');
                }
            });

            // Handle Edit Pipeline Form Submission
            $('#editPipelineForm').off('submit').on('submit', function(e) {
                console.log('Edit pipeline form submission triggered');
                e.preventDefault();
                e.stopPropagation();

                var form = $(this);

                // Check if edit is already in progress
                if (form.data('editing') || window.pipelineEditInProgress) {
                    console.log('Pipeline edit already in progress, preventing duplicate submission');
                    return false;
                }

                // Mark edit as in progress
                form.data('editing', true);
                window.pipelineEditInProgress = true;
                console.log('Starting pipeline edit AJAX request');

                // Ensure all stage inputs have proper names and values
                $('.stage-row').each(function(index) {
                    $(this).find('input[name*="[name]"]').attr('name', `stages[${index}][name]`);
                    $(this).find('input[name*="[id]"]').attr('name', `stages[${index}][id]`);
                    $(this).find('input[name*="[order]"]').attr('name', `stages[${index}][order]`).val(index + 1);
                });

                var formData = form.serialize();
                var actionUrl = form.attr('action');

                $.ajax({
                    url: actionUrl,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Reset edit flags
                        form.data('editing', false);
                        window.pipelineEditInProgress = false;

                        $('#editPipelineModal').modal('hide');

                        // Show success message
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', '{{__("Pipeline updated successfully!")}}');
                        } else {
                            alert('{{__("Pipeline updated successfully!")}}');
                        }

                        // Reload the page to show updated data
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        // Reset edit flags
                        form.data('editing', false);
                        window.pipelineEditInProgress = false;

                        var errorMessage = '{{__("An error occurred while updating the pipeline.")}}';

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            var errors = xhr.responseJSON.errors;
                            errorMessage = Object.values(errors).flat().join(', ');
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        if (typeof show_toastr === 'function') {
                            show_toastr('error', errorMessage);
                        } else {
                            alert(errorMessage);
                        }
                    }
                });

                return false;
            });

            // Handle Delete Pipeline Form Submission
            $('#deletePipelineForm').off('submit').on('submit', function(e) {
                console.log('Delete pipeline form submission triggered');
                e.preventDefault();
                e.stopPropagation();

                var form = $(this);

                // Check if deletion is already in progress
                if (form.data('deleting') || window.pipelineDeleteInProgress) {
                    console.log('Pipeline deletion already in progress, preventing duplicate submission');
                    return false;
                }

                // Mark deletion as in progress
                form.data('deleting', true);
                window.pipelineDeleteInProgress = true;
                console.log('Starting pipeline deletion AJAX request');

                var formData = form.serialize();
                var actionUrl = form.attr('action');

                $.ajax({
                    url: actionUrl,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Reset deletion flags
                        form.data('deleting', false);
                        window.pipelineDeleteInProgress = false;

                        $('#deletePipelineModal').modal('hide');

                        // Show success message
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', '{{__("Pipeline deleted successfully!")}}');
                        } else {
                            alert('{{__("Pipeline deleted successfully!")}}');
                        }

                        // Reload the page to show updated data
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        // Reset deletion flags
                        form.data('deleting', false);
                        window.pipelineDeleteInProgress = false;

                        var errorMessage = '{{__("An error occurred while deleting the pipeline.")}}';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        if (typeof show_toastr === 'function') {
                            show_toastr('error', errorMessage);
                        } else {
                            alert(errorMessage);
                        }
                    }
                });

                return false;
            });
        });
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Pipelines')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                data-bs-target="#createPipelineModal" data-bs-toggle="tooltip"
                title="{{__('Create New Pipeline & Stages')}}" id="createPipelineBtn">
            <i class="ti ti-plus me-1"></i>{{__('Create Pipeline & Stages')}}
        </button>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">{{__('Pipeline Management')}}</h5>
            <small class="text-muted">{{__('Manage your sales and lead pipelines')}}</small>
        </div>
        <div class="card-body table-border-style">
            <div class="table-responsive">
                <table class="table datatable">
                    <thead>
                        <tr>
                            <th>{{__('Pipeline Details')}}</th>
                            <th>{{__('Stages & Statistics')}}</th>
                            <th width="150px">{{__('Actions')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($pipelines as $pipeline)
                            @php
                                $leadStagesCount = $pipeline->leadStages ? $pipeline->leadStages->count() : 0;
                                $dealStagesCount = $pipeline->stages ? $pipeline->stages->count() : 0;

                                // Count leads in this pipeline
                                $leadsCount = \App\Models\Lead::where('pipeline_id', $pipeline->id)
                                    ->where('created_by', \Auth::user()->creatorId())
                                    ->where('is_converted', 0)
                                    ->count();
                            @endphp
                            <tr>
                                <td>
                                    <div class="pipeline-name">{{ $pipeline->name }}</div>
                                    <div class="pipeline-created">
                                        <i class="ti ti-calendar me-1"></i>
                                        {{__('Created')}} {{ $pipeline->created_at->format('M d, Y') }}
                                    </div>
                                </td>
                                <td>
                                    <div class="pipeline-stats">
                                        <span class="stat-badge lead-stages">
                                            <i class="ti ti-flag"></i>
                                            {{ $leadStagesCount }} {{__('Lead Stages')}}
                                        </span>
                                        <span class="stat-badge leads-count">
                                            <i class="ti ti-users"></i>
                                            {{ $leadsCount }} {{__('Leads')}}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        @can('edit pipeline')
                                            <a href="#" class="btn btn-sm btn-info btn-action"
                                               data-bs-toggle="modal"
                                               data-bs-target="#editPipelineModal"
                                               data-pipeline-id="{{ $pipeline->id }}"
                                               data-pipeline-name="{{ $pipeline->name }}"
                                               data-bs-toggle="tooltip" title="{{__('Edit Pipeline')}}">
                                                <i class="ti ti-pencil"></i>
                                            </a>
                                        @endcan
                                        @if(count($pipelines) > 1)
                                            @can('delete pipeline')
                                                <a href="#" class="btn btn-sm btn-danger btn-action"
                                                   data-bs-toggle="modal"
                                                   data-bs-target="#deletePipelineModal"
                                                   data-pipeline-id="{{ $pipeline->id }}"
                                                   data-pipeline-name="{{ $pipeline->name }}"
                                                   data-bs-toggle="tooltip" title="{{__('Delete Pipeline')}}">
                                                    <i class="ti ti-trash"></i>
                                                </a>
                                            @endcan
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Edit Pipeline Modal -->
    <div class="modal fade" id="editPipelineModal" tabindex="-1" aria-labelledby="editPipelineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPipelineModalLabel">{{__('Edit Pipeline & Stages')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editPipelineForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <!-- Pipeline Name Section -->
                        <div class="mb-4">
                            <h6 class="mb-3">{{__('Pipeline Information')}}</h6>
                            <div class="mb-3">
                                <label for="edit_pipeline_name" class="form-label">{{__('Pipeline Name')}}</label>
                                <input type="text" class="form-control" id="edit_pipeline_name" name="name" required maxlength="20">
                            </div>
                        </div>

                        <!-- Stages Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{__('Pipeline Stages')}}</h6>
                                <button type="button" class="btn btn-sm btn-primary" id="addStageBtn">
                                    <i class="ti ti-plus me-1"></i>{{__('Add Stage')}}
                                </button>
                            </div>
                            <div id="stagesContainer">
                                <!-- Stages will be loaded here dynamically -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('Cancel')}}</button>
                        <button type="submit" class="btn btn-primary">{{__('Update Pipeline & Stages')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Create Pipeline & Stages Modal -->
    <div class="modal fade" id="createPipelineModal" tabindex="-1" aria-labelledby="createPipelineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createPipelineModalLabel">{{__('Create Pipeline & Stages')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="createPipelineForm" method="POST" action="{{ route('pipelines.store') }}">
                    @csrf
                    <div class="modal-body">
                        <!-- Pipeline Name Section -->
                        <div class="mb-4">
                            <h6 class="mb-3">{{__('Pipeline Information')}}</h6>
                            <div class="mb-3">
                                <label for="create_pipeline_name" class="form-label">{{__('Pipeline Name')}}</label>
                                <input type="text" class="form-control" id="create_pipeline_name" name="name" required maxlength="20" placeholder="{{__('Enter Pipeline Name')}}">
                            </div>
                        </div>

                        <!-- Stages Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{__('Pipeline Stages')}}</h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-secondary me-2" id="loadDefaultStagesBtn">
                                        {{__('Load Defaults')}}
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary" id="addCreateStageBtn">
                                        <i class="ti ti-plus me-1"></i>{{__('Add Stage')}}
                                    </button>
                                </div>
                            </div>
                            <div id="createStagesContainer">
                                <!-- Default stages will be added here -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('Cancel')}}</button>
                        <button type="submit" class="btn btn-primary">{{__('Create Pipeline & Stages')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Pipeline Modal -->
    <div class="modal fade" id="deletePipelineModal" tabindex="-1" aria-labelledby="deletePipelineModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deletePipelineModalLabel">{{__('Delete Pipeline')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="deletePipelineForm" method="POST">
                    @csrf
                    @method('DELETE')
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="ti ti-alert-triangle me-2"></i>
                            {{__('Are you sure you want to delete this pipeline?')}}
                        </div>
                        <p class="mb-3">
                            {{__('Pipeline')}}: <strong id="delete_pipeline_name"></strong>
                        </p>

                        <!-- Checkbox for permanent deletion -->
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="permanentDeleteCheckbox" name="permanent_delete" value="1">
                            <label class="form-check-label text-danger fw-bold" for="permanentDeleteCheckbox">
                                {{__('Permanently delete pipeline and all associated data')}}
                            </label>
                        </div>

                        <!-- Warning message that appears when checkbox is checked -->
                        <div id="permanentDeleteWarning" class="alert alert-danger d-none">
                            <i class="ti ti-alert-circle me-2"></i>
                            <strong>{{__('WARNING:')}}</strong> {{__('You will lose all leads and lead stages associated with this pipeline. This action cannot be undone!')}}
                        </div>

                        <small class="text-muted">
                            {{__('Note: If checkbox is unchecked, pipeline will be soft deleted (marked as deleted but data preserved).')}}
                        </small>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('Cancel')}}</button>
                        <button type="submit" class="btn btn-danger" id="deleteConfirmBtn">{{__('Delete Pipeline')}}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
