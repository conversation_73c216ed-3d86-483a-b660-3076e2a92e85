<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\Deal;
use App\Models\Pipeline;
use App\Models\LeadStage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts from leads and deals.
     */
    public function index(Request $request)
    {
        $contacts = [];

        // Build leads query
        $leadsQuery = Lead::where('created_by', Auth::user()->creatorId());

        // Apply filters for leads
        if ($request->filled('filter_name')) {
            $leadsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_email')) {
            $leadsQuery->where('email', 'like', '%' . $request->filter_email . '%');
        }
        if ($request->filled('filter_phone')) {
            $leadsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch leads data with contact group information
        $leads = $leadsQuery->with('contactGroup')->get();
        foreach ($leads as $lead) {
            $contacts[] = [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead',
                'contact_group' => $lead->contactGroup ? $lead->contactGroup->name : null
            ];
        }

        // Build deals query
        $dealsQuery = Deal::where('created_by', Auth::user()->creatorId());

        // Apply filters for deals
        if ($request->filled('filter_name')) {
            $dealsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_phone')) {
            $dealsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch deals data
        $deals = $dealsQuery->get();
        foreach ($deals as $deal) {
            $contacts[] = [
                'id' => $deal->id,
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal'
            ];
        }

        // External contacts are now loaded via JavaScript on the frontend
        // This provides better performance and handles API connectivity issues gracefully

        // Apply type filter (only for internal contacts - leads and deals)
        if ($request->filled('filter_type') && $request->filter_type !== 'WhatsApp') {
            $contacts = array_filter($contacts, function($contact) use ($request) {
                return $contact['type'] === $request->filter_type;
            });
        }

        // Get enabled module integration for OMX Flow (same as leads)
        $omxFlowModule = \App\Models\ModuleIntegration::enabled()
            ->whereNotNull('base_url')
            ->where('base_url', '!=', '')
            ->first();

        // Get pipelines for convert to lead modal
        $pipelines = Pipeline::where('created_by', Auth::user()->creatorId())->get();

        // Create default pipeline if none exists
        if ($pipelines->isEmpty()) {
            $pipeline = Pipeline::create([
                'name' => 'Default Pipeline',
                'created_by' => Auth::user()->creatorId()
            ]);

            // Create default stages for the pipeline
            $defaultStages = ['New', 'Qualified', 'Discussion', 'Negotiation', 'Won/Lost'];
            foreach($defaultStages as $order => $stageName) {
                LeadStage::create([
                    'name' => $stageName,
                    'pipeline_id' => $pipeline->id,
                    'created_by' => Auth::user()->creatorId(),
                    'order' => $order
                ]);
            }

            // Re-fetch pipelines after creating defaults
            $pipelines = Pipeline::where('created_by', Auth::user()->creatorId())->get();
        }

        return view('contacts.index', compact('contacts', 'omxFlowModule', 'pipelines'));
    }

    /**
     * Check which external contact emails have already been converted to leads
     */
    public function checkConvertedContacts(Request $request)
    {
        $emails = $request->input('emails', []);

        if (empty($emails)) {
            return response()->json(['converted_emails' => []]);
        }

        // Check if any of these emails already exist as leads
        $convertedEmails = Lead::where('created_by', Auth::user()->creatorId())
            ->whereIn('email', $emails)
            ->pluck('email')
            ->map(function($email) {
                return strtolower($email);
            })
            ->toArray();

        return response()->json(['converted_emails' => $convertedEmails]);
    }

    /**
     * Get stages for a specific pipeline
     */
    public function getPipelineStages(Request $request)
    {
        $pipelineId = $request->input('pipeline_id');

        \Log::info('ContactController: Getting stages for pipeline', [
            'pipeline_id' => $pipelineId,
            'user_id' => Auth::id(),
            'creator_id' => Auth::user()->creatorId(),
            'request_data' => $request->all()
        ]);

        if (!$pipelineId) {
            \Log::warning('No pipeline ID provided');
            return response()->json([
                'success' => false,
                'message' => 'Pipeline ID is required',
                'stages' => []
            ]);
        }

        try {
            $stages = \App\Models\LeadStage::where('pipeline_id', $pipelineId)
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('order', 'asc')
                ->orderBy('id', 'asc')
                ->get(['id', 'name', 'order']);

            \Log::info('ContactController: Found stages', [
                'pipeline_id' => $pipelineId,
                'count' => $stages->count(),
                'stages' => $stages->toArray()
            ]);

            return response()->json([
                'success' => true,
                'stages' => $stages,
                'debug' => [
                    'pipeline_id' => $pipelineId,
                    'creator_id' => Auth::user()->creatorId(),
                    'stages_count' => $stages->count()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('ContactController: Error fetching stages', [
                'error' => $e->getMessage(),
                'pipeline_id' => $pipelineId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error fetching stages: ' . $e->getMessage(),
                'stages' => []
            ]);
        }
    }

    /**
     * Update contact pipeline assignment (for both Leads and Deals)
     */
    public function updateContactPipeline(Request $request, $id)
    {
        \Log::info('ContactController: Updating contact pipeline', [
            'contact_id' => $id,
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        $validator = \Validator::make($request->all(), [
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
            'notes' => 'nullable|string',
            'contact_type' => 'nullable|string|in:Lead,Deal'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
                'errors' => $validator->errors()
            ], 422);
        }

        // Try to find as Lead first, then as Deal
        $contact = Lead::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        $contactType = 'Lead';

        if (!$contact) {
            $contact = \App\Models\Deal::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();
            $contactType = 'Deal';
        }

        if (!$contact) {
            return response()->json([
                'success' => false,
                'message' => 'Contact not found'
            ], 404);
        }

        // Store original values for change tracking
        $originalStageId = $contact->stage_id ?? null;
        $originalPipelineId = $contact->pipeline_id ?? null;

        // Update pipeline and stage
        $contact->pipeline_id = $request->pipeline_id;
        $contact->stage_id = $request->stage_id;

        // Update notes if provided
        if ($request->filled('notes')) {
            $contact->notes = $request->notes;
        }

        $contact->save();

        // Get pipeline and stage names for response
        $pipeline = Pipeline::find($request->pipeline_id);
        $stage = \App\Models\LeadStage::find($request->stage_id);

        \Log::info('Contact pipeline updated successfully', [
            'contact_id' => $id,
            'contact_name' => $contact->name,
            'contact_type' => $contactType,
            'pipeline_name' => $pipeline->name,
            'stage_name' => $stage->name
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact moved to pipeline successfully!',
            'contact' => [
                'id' => $contact->id,
                'name' => $contact->name,
                'type' => $contactType,
                'pipeline_id' => $contact->pipeline_id,
                'stage_id' => $contact->stage_id,
                'pipeline_name' => $pipeline->name,
                'stage_name' => $stage->name
            ]
        ]);
    }

    /**
     * Fetch external contacts from API
     */
    private function fetchExternalContacts(Request $request)
    {
        $externalContacts = [];

        try {
            // Generate SSO token (similar to leads list view)
            $ssoToken = $this->generateSsoToken();

            if ($ssoToken) {
                $baseUrl = config('app.url');
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $ssoToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'X-User-Email' => Auth::user()->email
                ])->get($baseUrl . '/api/sso/contacts/simple-list');

                if ($response->successful()) {
                    $data = $response->json();

                    if (isset($data['data']['contacts']) && is_array($data['data']['contacts'])) {
                        foreach ($data['data']['contacts'] as $contact) {
                            $contactName = $contact['full_name'] ?? $contact['name'] ?? 'N/A';
                            $contactEmail = $contact['email'] ?? '';

                            // Apply filters for external contacts
                            $includeContact = true;

                            if ($request->filled('filter_name') && stripos($contactName, $request->filter_name) === false) {
                                $includeContact = false;
                            }

                            if ($request->filled('filter_email') && stripos($contactEmail, $request->filter_email) === false) {
                                $includeContact = false;
                            }

                            if ($includeContact) {
                                $externalContacts[] = [
                                    'id' => 'ext_' . $contact['id'],
                                    'name' => $contactName,
                                    'email' => $contactEmail,
                                    'phone' => '', // External contacts might not have phone
                                    'type' => 'WhatsApp',
                                    'source' => 'external',
                                    'source_id' => $contact['id'],
                                    'external_id' => $contact['id']
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the page
            Log::error('Failed to fetch external contacts: ' . $e->getMessage());
        }

        return $externalContacts;
    }

    /**
     * Generate SSO token for external API
     */
    private function generateSsoToken()
    {
        try {
            $baseUrl = config('app.url');
            $response = Http::post($baseUrl . '/api/sso/generate-token', [
                'email' => Auth::user()->email,
                'name' => Auth::user()->name
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['token'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to generate SSO token: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Update lead via AJAX
     */
    public function updateLead(Request $request, $id)
    {
        $lead = Lead::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'email' => 'nullable|email',
            'phone' => 'nullable|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $lead->name = $request->name;
        $lead->email = $request->email;
        $lead->phone = $request->phone;
        $lead->save();

        return response()->json(['success' => 'Lead updated successfully']);
    }

    /**
     * Update deal via AJAX
     */
    public function updateDeal(Request $request, $id)
    {
        $deal = Deal::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'phone' => 'nullable|max:20',
            'price' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $deal->name = $request->name;
        $deal->phone = $request->phone;
        $deal->price = $request->price ?? 0;
        $deal->save();

        return response()->json(['success' => 'Deal updated successfully']);
    }
}
